
<!DOCTYPE html>
<html lang="en">
<head>     <link rel="icon" type="image/x-icon" href="../assets/images/favicon.ico?v=2">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Blog - Sahla Smart Solutions</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>tailwind.config={theme:{extend:{colors:{primary:'#0056b3',secondary:'#4dabf7'},borderRadius:{'none':'0px','sm':'4px',DEFAULT:'8px','md':'12px','lg':'16px','xl':'20px','2xl':'24px','3xl':'32px','full':'9999px','button':'8px'}}}}</script>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/navigation.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@4.5.0/fonts/remixicon.css" rel="stylesheet">
    <script src="../assets/js/blog-posts.js"></script>
    <style>
        :where([class^="ri-"])::before { content: "\f3c2"; }
        /* Blog Hero */
        .blog-hero {
            background-image: linear-gradient(to bottom, #111827d8, #22267a8f), url('https://images.unsplash.com/photo-1432821596592-e2c18b78144f?fm=jpg&q=60&w=3000&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8M3x8YmxvZyUyMGNvdmVyfGVufDB8fDB8fHww');
            background-size: 50% auto;
            background-position: center top 20%;
            padding: 8rem 0 4rem;
        }

        .tag-filter-btn{
            background-color: var(--background-color-light);
            color: var(--text-color);
            border: 1px solid var(--primary-color-1);
            border-radius: 1rem;
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
            cursor: pointer;
        }

        .tag-filter-btn:hover{
            background-color: var(--background-color);
            color: var(--text-color);
        }
        
        .tag-filter-btn:active{
            background-color: var(--background-color);
            color: var(--text-color);
        }
        /* Blog Grid and Sidebar */
        .blog-content .grid {
            gap: 4rem;
        }

        .blog-content .category .hover{
            background-color: #0056b3;  
        }

        /* Sidebar Categories */
        .blog-categories ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .blog-categories li {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid var(--border-color);
        }

        .blog-categories li:last-child {
            border-bottom: none;
        }

        .category-count {
            background-color: var(--background-color-light);
            color: var(--text-light);
            border-radius: 1rem;
            padding: 0.25rem 0.75rem;
            font-size: 0.75rem;
        }

        .blog-card {
            border: 1px solid var(--border-color);
            border-radius: 1rem;
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .blog-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        .blog-card-image {
            height: 200px;
            overflow: hidden;
        }

        .blog-card-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.5s ease;
        }

        .blog-card:hover .blog-card-image img {
            transform: scale(1.05);
        }

        .blog-content {
            padding: 1.5rem;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }

        .blog-meta {
            display: flex;
            gap: 1rem;
            margin: 0.5rem 0;
            color: var(--text-light);
            font-size: 0.875rem;
        }

        .blog-meta span {
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .blog-excerpt {
            margin: 1rem 0;
            color: var(--text-color);
            flex-grow: 1;
        }

        .blog-tag {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            background-color: var(--background-color-light);
            color: var(--primary-color-1);
            border-radius: 2rem;
            border: 1px solid var(--border-color);
            font-size: 0.75rem;
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
        }

        .pagination-btn{
            background-color: var(--background-color-light);
            color: var(--text-color);
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
            cursor: pointer;
        }

        .pagination-btn:hover{
            background-color: var(--background-color);
            color: var(--text-color);
        }

        .pagination-btn:active{
            background-color: var(--background-color);
            color: var(--text-color);
        }

        .blog-tags {
            display: inline-block;
        }

        .category-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .category-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        .category-icon {
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--primary-color);
            /* color: white; */
            border-radius: 50%;
            font-size: 1.5rem;
        }

        .search-box {
            position: relative;
            width: 100%;
            display: flex;
            align-items: center;
            gap: 8px;
            background-color: var(--background-color);
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            margin-bottom: 1.5rem;
        }

        .category-title {
            font-size: 1.125rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .category-link {
            color: var(--primary);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .category-link:hover {
            color: var(--primary-dark);
        }

        /* Tag Cloud */
        .tag-cloud-item {
            border: 1px solid var(--primary-color-1);
            background: var(--background-color-light);
            color: var(--text-color);
            padding: 0.5rem 1rem;
            border-radius: 2rem;
            font-size: 0.875rem;
            text-decoration: none;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .tag-cloud-item:hover {
            background: var(--primary-color);
            color: var(--primary-color-1);
            border-color: var(--primary-color);
        }

        .tag-count {
            background: var(--bg-card);
            color: var(--text-light);
            padding: 0.25rem 0.5rem;
            border-radius: 1rem;
            font-size: 0.75rem;
        }

        .tag-cloud-item:hover .tag-count {
            background: rgba(255, 255, 255, 0.2);
            color: var(--primary-color-1);
        }
        
        /* Search Box */
       .search-box {
            position: relative;
            margin-bottom: 2rem;
        }

        .search-box i {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-light);
        }

        .search-box input {
            width: 100%;
            padding: 0.75rem 1rem 0.75rem 2.5rem;
            border-radius: 0.5rem;
            border: 1px solid var(--border);
            background: var(--bg-card);
            color: var(--text);
            transition: all 0.3s ease;
        }

        .search-box input:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.1);
        }


        .search-box input::placeholder {
            color: var(--text-light);
        }

        .sidebar-widget {
            background-color: var(--background-color-light);
            border: 1px solid var(--border-color);
            border-radius: 1rem;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .sidebar-widget h3 {
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid var(--border-color);
        }

        .recent-post {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border-color);
        }

        .recent-post:last-child {
            margin-bottom: 0;
            padding-bottom: 0;
            border-bottom: none;
        }

        .recent-post-image {
            width: 80px;
            height: 80px;
            border-radius: 0.5rem;
            overflow: hidden;
        }

        .recent-post-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .recent-post-content h4 {
            font-size: 0.875rem;
            margin-bottom: 0.25rem;
        }

        .recent-post-date {
            font-size: 0.75rem;
            color: var(--text-light);
        }

        .tag-cloud {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        @media (max-width: 768px) {
            .blog-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body class="bg-light dark:bg-dark text-text dark:text-light-text">
    <!-- Header -->
 <header class="header">
    <div class="container">
      <nav class="nav">
        <a href="../index.html" class="nav-logo">
          <img src="../assets/images/Main Horizontal Version.svg" alt="Sahla Logo" class="logo-img" id="nav-logo">
        </a>
        <input type="checkbox" id="mobile-menu-toggle" class="mobile-menu-toggle">
        <label for="mobile-menu-toggle" class="mobile-menu-button">
          <span></span>
          <span></span>
          <span></span>
        </label>
        <div class="nav-links">
          <div class="dropdown">
            <a href="#services" class="nav-link">Services</a>
            <div class="dropdown-content">
              <a href="./process-automation.html" class="dropdown-link">Process Automation</a>
              <a href="./technology-consulting.html" class="dropdown-link">Technology Consulting</a>
            </div>
          </div>
          <a href="./ventures.html" class="nav-link">Our Ventures</a>
          <a href="./partners.html" class="nav-link">Partners</a>
          <a href="./careers.html" class="nav-link">Careers</a>
          <a href="./blog.html" class="nav-link">Blog</a>
          <a href="./about.html" class="nav-link">About us</a>
          <div class="px-4">
            <div class="h-6 border-l-2 border-gray-300"></div>
          </div>
          <a href="./contact.html" class="px-6 py-2 !rounded-button hover:bg-opacity-90 transition-all" style="background-color: var(--background-color-light); color: var(--text-color);">Contact Sales</a>
          <div class="hidden md:flex items-center gap-4">
            <div class="theme-switch" id="theme-switch">
              <i class="ri-moon-line moon-icon"></i>
              <i class="ri-sun-line sun-icon"></i>
              <script>
                (() => {
                  const savedTheme = localStorage.getItem('theme');
                  const preferred = savedTheme || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                  document.documentElement.setAttribute('data-theme', preferred);
                })();
              </script>
            </div>
          </div>
        </div>
      </nav>
    </div>
  </header>

    <!-- Hero Section -->
    <section class="blog-hero pt-32 pb-16" >
        <div class="container mx-auto px-4" style="margin-top: 10%;">
            <h1 class="text-4xl md:text-5xl font-bold text-align:start mb-6" style="color: white;">Our Blog</h1>
            <p class="text-xl text-align:start max-w-2xl" style="color: white;">Insights, updates, and thought leadership on AI, automation, and digital transformation.</p>
        </div>
    </section>

    <!-- Featured Posts -->
    <section class="featured-posts py-16 bg-light dark:bg-dark-alt">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold mb-12 text-center">Featured Posts</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Posts will be dynamically inserted here -->
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <section class="blog-content py-16" style="background-color: var(--background-color);">
        <div class="container mx-auto px-4">
            <div class="text-center mb-8">
                <h2 class="section-title">Latest Posts</h2>
                <p class="section-subtitle">Stay updated with our latest insights</p>
            </div>
            <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
                <!-- Blog Posts Section – Now takes 3/4 of the grid -->
                <div class="lg:col-span-3">
                    <div class="blog-posts grid grid-cols-1 md:grid-cols-2 gap-8">
                        <!-- Posts will be dynamically inserted here -->
                    </div>
                    <!-- Pagination Controls -->
                    <div class="pagination mt-8 flex justify-center">
                        <!-- Pagination buttons will be dynamically inserted here -->
                    </div>
                </div>
    
                <!-- Sidebar – Now takes 1/4 of the grid -->
                <div class="lg:col-span-1">
                    <!-- Search Widget -->
                    <div class="sidebar-widget">
                        <h3 class="text-xl font-bold mb-4">Search</h3>
                        <div class="search-box">
                            <i class="ri-search-line"></i>
                            <input type="text" placeholder="Search articles...">
                        </div>
                    </div>
    
                    <!-- Categories -->
                    <div class="blog-categories mb-8 sidebar-widget">
                        <h3 class="text-xl font-bold mb-4">Categories</h3>
                        <div class="category grid-cols-1 gap-4">
                            <!-- Categories will be dynamically inserted here -->
                        </div>
                    </div>
                    <!-- Recent Posts Widget -->
                    <div class="sidebar-widget">
                        <h3 class="text-xl font-bold mb-4">Recent Posts</h3>
                        <div class="recent-posts">
                          <!-- Recent posts will be dynamically inserted here -->
                        </div>
                      </div>
                      
                    <!-- Tag Cloud -->
                    <div class="tag-cloud sidebar-widget">
                        <h3 class="text-xl font-bold mb-4">Popular Tags</h3>
                        <div class="flex flex-wrap gap-2">
                            <!-- Tags will be dynamically inserted here -->
                        </div>
                    </div>                
                </div>
            </div>
        </div>
    </section>
    

    <!-- Newsletter -->
    <section class="newsletter py-16 bg-primary text-white">
        <div class="container mx-auto px-4 text-center">
            <h2 class="text-3xl font-bold mb-4">Subscribe to Our Newsletter</h2>
            <p class="mb-8 max-w-2xl mx-auto">Stay updated with our latest insights and articles on AI, automation, and digital transformation.</p>
            <form class="newsletter-form max-w-md mx-auto">
                <div class="flex gap-4">
                    <input type="email" placeholder="Enter your email" class="flex-1 px-4 py-2 rounded-lg text-dark">
                    <button type="submit" class="btn btn-light">Subscribe</button>
                </div>
            </form>
        </div>
    </section>

<!-- f -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <!-- Company Info Column -->
                <div class="footer-info">
                    <a href="../index.html" class="footer-logo">
                        <img src="../assets/images/Negative Horizontal Version.svg" alt="Sahla Smart Solutions" class="logo-img" id="foot-logo">
                    </a>
                    <p class="footer-description">Empowering businesses through innovative venture building and automation solutions in the MENA region.</p>
                    <div class="footer-social">
                        <a href="https://eg.linkedin.com/company/sahla-solutions" target="_blank" rel="noopener noreferrer"><i class="ri-linkedin-fill"></i></a>
                        <a href="https://x.com/sahlasolutions" target="_blank" rel="noopener noreferrer"><i class="ri-twitter-fill"></i></a>
                        <a href="https://www.facebook.com/SahlaSmartSolutions/" target="_blank" rel="noopener noreferrer"><i class="ri-facebook-fill"></i></a>
                        <a href="https://www.instagram.com/sahlasolutions/" target="_blank" rel="noopener noreferrer"><i class="ri-instagram-fill"></i></a>
                    </div>
                </div>

                <!-- Quick Links Column -->
                <div class="footer-links-column">
                    <h4>Quick Links</h4>
                    <ul class="footer-links-list">
                        <li><a href="../index.html">Home</a></li>
                        <li><a href="./about.html">About Us</a></li>
                        <li><a href="./ventures.html">Our Ventures</a></li>
                        <li><a href="./partners.html">Partners</a></li>
                        <li><a href="./careers.html">Careers</a></li>
                        <li><a href="./contact.html">Contact Us</a></li>
                    </ul>
                </div>

                <!-- Services Column -->
                <div class="footer-links-column">
                    <h4>Services</h4>
                    <ul class="footer-links-list">
                        <li><a href="./process-automation.html">Process Automation</a></li>
                        <li><a href="./technology-consulting.html">Technology Consulting</a></li>
                    </ul>
                </div>

                <!-- Newsletter Column -->
                <div class="footer-newsletter">
                    <h4>Newsletter</h4>

                    <form class="mb-4">
                        <div class="flex">
                            <!-- <input type="email" placeholder="Your email" class="w-full px-4 py-2 bg-gray-800 border-none text-white text-sm rounded-l"> -->
                            <a href="javascript:void(0)" class="ml-onclick-form" onclick="ml('show', 'gturk0', true)" style="
                                display: inline-block;
                                padding: 8px 16px;
                                background-color: var(--primary-color-1);
                                color: white;
                                font-size: 16px;
                                font-family: sans-serif;
                                border: none;
                                border-radius: 6px;
                                text-decoration: none;
                                cursor: pointer;
                                transition: background-color 0.3s ease;
                                " onmouseover="this.style.backgroundColor='var(--primary-color-2)'" onmouseout="this.style.backgroundColor= 'var(--primary-color-3)'">
                                Subscribe to Newsletter
                                </a>
                        </div>
                    </form>
                    <p>Subscribe to our newsletter to stay updated with our latest news and insights.</p>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-12 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <p class="text-gray-400 text-sm mb-4 md:mb-0">© 2025 Sahla Smart Solutions. All rights reserved.</p>
                    <div class="flex space-x-6">
                        <a href="../pages/privacy-policy.html" class="text-gray-400 hover:text-white text-sm">Privacy Policy</a>
                        <a href="../pages/terms-of-service.html" class="text-gray-400 hover:text-white text-sm">Terms of Service</a>
                        <a href="../pages/cookies-policy.html" class="text-gray-400 hover:text-white text-sm">Cookies Policy</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Theme switch functionality
        const themeSwitch = document.getElementById('theme-switch');
        const html = document.documentElement;
        const navLogo = document.getElementById('nav-logo');
        const footLogo = document.getElementById('foot-logo');

        // Function to set and save theme
        const setTheme = (theme) => {
            html.setAttribute('data-theme', theme);
            localStorage.setItem('theme', theme);
            themeSwitch.classList.toggle('dark', theme === 'dark');

            // Update logo based on theme
            if (theme === 'dark') {
                navLogo.src = '../assets/images/Dark Horizontal Version.svg';
                footLogo.src = '../assets/images/Dark Horizontal Version.svg';
            } else {
                navLogo.src = '../assets/images/Main Horizontal Version.svg';
                footLogo.src = '../assets/images/Main Horizontal Version.svg';
            }
        };

        // Set the correct theme class on icon container and logo after load
        document.addEventListener('DOMContentLoaded', () => {
            const currentTheme = html.getAttribute('data-theme');
            themeSwitch.classList.toggle('dark', currentTheme === 'dark');

            // Set initial logo based on theme
            if (currentTheme === 'dark') {
                navLogo.src = '../assets/images/Dark Horizontal Version.svg';
                footLogo.src = '../assets/images/Dark Horizontal Version.svg';
            } else {
                navLogo.src = '../assets/images/Main Horizontal Version.svg';
                footLogo.src = '../assets/images/Main Horizontal Version.svg';
            }
        });

        // Toggle theme on click
        themeSwitch.addEventListener('click', () => {
            const isCurrentlyDark = html.getAttribute('data-theme') === 'dark';
            const newTheme = isCurrentlyDark ? 'light' : 'dark';
            setTheme(newTheme);
        });

        // Watch for system theme changes
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
            if (!localStorage.getItem('theme')) {
                setTheme(e.matches ? 'dark' : 'light');
            }
        });

        // Mobile menu toggle
        const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
        const navLinks = document.querySelector('.nav-links');

        mobileMenuToggle.addEventListener('change', function() {
            if (this.checked) {
                navLinks.style.display = 'flex';
            } else {
                navLinks.style.display = '';
            }
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function(e) {
                e.preventDefault();

                const targetId = this.getAttribute('href');
                if (targetId === '#') return;

                const targetElement = document.querySelector(targetId);
                if (targetElement) {
                    const headerHeight = 80; // Approximate header height
                    const targetPosition = targetElement.getBoundingClientRect().top + window.pageYOffset - headerHeight;

                    window.scrollTo({
                        top: targetPosition,
                        behavior: 'smooth'
                    });

                    // Close mobile menu if open
                    if (!mobileMenuToggle.checked) {
                        mobileMenuToggle.checked = false;
                        navLinks.style.display = '';
                    }
                }
            });
        });

        // Sticky header effect
        window.addEventListener('scroll', () => {
            const header = document.querySelector('header');
            if (window.scrollY > 50) {
                header.classList.add('shadow-md');
            } else {
                header.classList.remove('shadow-md');
            }
        });

        // Newsletter form submission
        const newsletterForm = document.querySelector('.newsletter-form');
        newsletterForm.addEventListener('submit', (e) => {
            e.preventDefault();
            const email = e.target.querySelector('input[type="email"]').value;
            // Add your newsletter subscription logic here
            alert('Thank you for subscribing to our newsletter!');
            e.target.reset();
        });
    </script>
</body>
<script>'undefined'=== typeof _trfq || (window._trfq = []);'undefined'=== typeof _trfd && (window._trfd=[]),_trfd.push({'tccl.baseHost':'secureserver.net'},{'ap':'cpsh-oh'},{'server':'sxb1plzcpnl453516'},{'dcenter':'sxb1'},{'cp_id':'9153305'},{'cp_cache':''},{'cp_cl':'8'}) // Monitoring performance to make your website faster. If you want to opt-out, please contact web hosting support.</script><script src='https://img1.wsimg.com/traffic-assets/js/tccl.min.js'></script></html>
