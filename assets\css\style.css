/* Base styles */

:root {
    --primary-color-1: #2563eb;
    --primary-color-2: #1d4ed8;
    --primary-color-3: #22277A;
    --primary-color-4: #22277A;
    --secondary-color-1: #10b981;
    --secondary-color-2: #059669;
    --text-color: #1f2937;
    --text-color-light: #6b7280;
    --background-color: #ffffff;
    --background-color-light: #f3f4f6;
    --border-color: #e5e7eb;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --gradient-primary: linear-gradient(135deg, #0460BF 0%, #48079F 100%);
    --bg-gradient-light: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

[data-theme="dark"] {
    --text-color: #f3f4f6;
    --text-color-light: #9ca3af;
    --background-color: #111827;
    --background-color-light: #1f2937;
    --border-color: #374151;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4);
    --bg-gradient-light: linear-gradient(135deg, #1f2937 0%, #111827 100%);
}

html {
    transition: color 0.3s ease, background-color 0.3s ease;
}

html * {
    transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

body {
    margin: 0;
    padding: 0;
    font-family: 'Roboto', sans-serif;
    color: var(--text-color);
    line-height: 1.5;
    background-color: var(--background-color);
}


/* Theme Switch */

.theme-switch {
    width: 48px;
    height: 24px;
    background-color: var(--background-color-light);
    border-radius: 12px;
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
}

.theme-switch::after {
    content: "";
    position: absolute;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: var(--primary-color-1);
    top: 2px;
    left: 2px;
    transition: all 0.3s ease;
}

.theme-switch.dark::after {
    transform: translateX(24px);
    background-color: #fbbf24;
}

.pagination-btn {
    background-color: var(--background-color-light);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    cursor: pointer;
}

.pagination-btn:hover {
    background-color: var(--background-color);
    color: var(--text-color);
}

.pagination-btn:active {
    background-color: var(--background-color);
    color: var(--text-color);
}

.pagination-number {
    background-color: var(--background-color-light);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    cursor: pointer;
}

.pagination-number:hover {
    background-color: var(--background-color);
    color: var(--text-color);
}

.pagination-number:active {
    background-color: var(--background-color);
    color: var(--text-color);
}

.theme-switch i {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    font-size: 14px;
    color: var(--text-color);
}

.theme-switch .sun-icon {
    right: 6px;
}

.theme-switch .moon-icon {
    left: 6px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

.hero-section {
    background-image: linear-gradient(to bottom, #111827d8, #22267a8f), url('https://images.pexels.com/photos/8386440/pexels-photo-8386440.jpeg');
    background-size: cover;
    background-position: center;
}

.process-line {
    background: linear-gradient(90deg, var(--primary-color-1) 0%, var(--secondary-color-1) 100%);
    height: 2px;
    position: absolute;
    top: 3rem;
    left: 2.5rem;
    right: 2.5rem;
    z-index: 0;
}

.process-step {
    z-index: 1;
    position: relative;
}

.process-number {
    width: 3rem;
    height: 3rem;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: white;
    border: 2px solid var(--primary-color-1);
    color: var(--primary-color-1);
    font-weight: 700;
}


/* Partner section styles */

.partners-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.partner-logo-card {
    aspect-ratio: 1;
    width: 100%;
    max-width: 200px;
    margin: 0 auto;
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.partner-logo-card.visible {
    opacity: 1;
    transform: translateY(0);
}

.partner-logo-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 1.5rem;
    background: var(--bg-light);
    border-radius: 1rem;
    transition: all 0.3s ease;
}

.partner-logo-content:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.partner-icon-wrapper {
    width: 64px;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    font-size: 2rem;
    color: var(--primary-color);
}

.partner-name {
    text-align: center;
    font-weight: 500;
    color: var(--text-color);
}

.filter-btn {
    padding: 0.5rem 1.5rem;
    border-radius: 2rem;
    background: transparent;
    color: var(--primary-color);
    border: 1px solid var(--border-color);
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-btn:hover {
    background: var(--primary-color);
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.filter-btn.active {
    background: var(--primary-color);
    color: var(--primary-color);
    border-color: var(--primary-color);
}


/* Success Stories Carousel */

.success-stories-carousel {
    position: relative;
    max-width: 1000px;
    margin: 0 auto;
    overflow: hidden;
}

.carousel-container {
    display: flex;
    transition: transform 0.5s ease;
}

.story-card {
    min-width: 100%;
    padding: 2rem;
    box-sizing: border-box;
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.5s ease, transform 0.5s ease;
}

.story-card.visible {
    opacity: 1;
    transform: translateY(0);
}

.story-card-content {
    background: var(--background-color);
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    height: 100%;
}

.story-header {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
}

.story-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin-right: 1rem;
    object-fit: cover;
}

.story-info h3 {
    margin: 0;
    font-size: 1.25rem;
    color: var(--text-color);
}

.story-info p {
    margin: 0.25rem 0 0;
    font-size: 0.875rem;
    color: var(--text-light);
}

.story-quote {
    font-style: italic;
    color: var(--text-color);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.story-metrics {
    display: flex;
    justify-content: space-between;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

.metric-item {
    text-align: center;
}

.metric-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.metric-label {
    font-size: 0.75rem;
    color: var(--text-light);
}

.carousel-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 2rem;
}

.carousel-control-btn {
    background: transparent;
    border: none;
    color: var(--text-color);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    transition: color 0.3s ease;
}

.carousel-control-btn:hover {
    color: var(--primary-color);
}

.carousel-indicators {
    display: flex;
    gap: 0.5rem;
    margin: 0 1rem;
}

.indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--border-color);
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.3s ease;
}

.indicator.active {
    background: var(--primary-color-1);
    transform: scale(1.2);
}


/* Why Work With Us Section */

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.feature-card {
    border: 1px solid var(--border-color);
    background: var(--bg-light);
    border-radius: 1rem;
    padding: 2rem;
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.feature-icon {
    width: 64px;
    height: 64px;
    margin: 0 auto 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-color);
    color: var(--primary-color-1);
    border-radius: 50%;
    font-size: 1.5rem;
}

.feature-card h3 {
    margin-bottom: 1rem;
    color: var(--text-color);
}

.feature-card p {
    color: var(--text-light);
    line-height: 1.6;
}


/* === HEADER STYLES FOR SAHLA === */


/* === HEADER STYLES FOR SAHLA === */

.h1 {
    font-family: 'Nexa', sans-serif;
    font-size: 2.25rem;
    /* Equivalent to text-4xl */
    font-weight: 800;
    /* Equivalent to font-extrabold */
    line-height: 1.25;
    /* Equivalent to leading-tight */
    letter-spacing: -0.02em;
    /* Equivalent to tracking-tight */
    text-align: left;
}

@media (min-width: 768px) {
    .h1 {
        font-size: 3rem;
        /* Equivalent to text-5xl on md screens */
    }
}

.h2 {
    font-size: 1.875rem;
    /* Equivalent to text-3xl */
    font-weight: 700;
    /* Equivalent to font-bold */
    margin-bottom: 2rem;
    /* Equivalent to mb-4 */
    letter-spacing: 0.05em;
    /* Equivalent to tracking-wide */
}

.h3 {
    font-size: 1.5rem;
    /* Equivalent to text-2xl */
    font-weight: 600;
    /* Equivalent to font-semibold */
    margin-bottom: 0.75rem;
    /* Equivalent to mb-3 */
}

.h4 {
    font-size: 1.25rem;
    /* Equivalent to text-xl */
    font-weight: 500;
    /* Equivalent to font-medium */
    text-transform: uppercase;
    /* Equivalent to uppercase */
    letter-spacing: 0.05em;
    /* Equivalent to tracking-wide */
    margin-bottom: 0.5rem;
    /* Equivalent to mb-2 */
}

.h5 {
    font-size: 1.125rem;
    /* Equivalent to text-lg */
    font-weight: 500;
    /* Equivalent to font-medium */
}

.h6 {
    font-size: 0.875rem;
    /* Equivalent to text-sm */
    font-weight: 300;
    /* Equivalent to font-light */
    text-transform: uppercase;
    /* Equivalent to uppercase */
    letter-spacing: 0.1em;
    /* Equivalent to tracking-widest */
}

.p1 {
    text-align: left;
}


/* Common sections */

.hero-section {
    background: var(--gradient-primary);
    background-size: cover;
    background-position: center;
    padding: 8rem 2rem 6rem;
    color: white;
    text-align: center;
    min-height: 600px;
    display: flex;
    align-items: center;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.section {
    padding: 5rem 0;
    background-color: var(--background-color);
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    text-align: center;
    color: var(--text-color);
}

.section-subtitle {
    font-size: 1.25rem;
    color: var(--text-color-light);
    max-width: 800px;
    margin: 0 auto 3rem;
    text-align: center;
}


/* Cards */

.card {
    background-color: var(--background-color);
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    padding: 2rem;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
    border: 1px solid var(--border-color);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}


/* Buttons */

.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    border-radius: 0.375rem;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: var(--primary-color-1);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-color-2);
}

.btn-secondary {
    background-color: var(--secondary-color-1);
    color: white;
}

.btn-secondary:hover {
    background-color: var(--secondary-color-2);
}

.btn-outline {
    background: transparent;
    border: 2px solid white;
    color: white;
}

.btn-outline:hover {
    background: rgba(255, 255, 255, 0.1);
}

.btn-light {
    background-color: white;
    color: var(--primary-color-1);
}

.btn-light:hover {
    background-color: var(--background-color-light);
}


/* Forms */

.form-input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    margin-bottom: 1rem;
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-color-1);
    box-shadow: 0 0 0 3px rgba(4, 96, 191, 0.1);
}


/* Grid layouts */

.grid {
    display: grid;
    gap: 2rem;
}

.grid-2 {
    grid-template-columns: repeat(2, 1fr);
}

.grid-3 {
    grid-template-columns: repeat(3, 1fr);
}

.grid-4 {
    grid-template-columns: repeat(4, 1fr);
}

.grid-5 {
    grid-template-columns: repeat(5, 1fr);
}

@media (max-width: 1024px) {
    .grid-4,
    .grid-5 {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 768px) {
    .grid-3,
    .grid-4,
    .grid-5 {
        grid-template-columns: repeat(2, 1fr);
    }
    .grid-2 {
        grid-template-columns: 1fr;
    }
    .section {
        padding: 3rem 0;
    }
    .section-title {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .grid-3,
    .grid-4,
    .grid-5 {
        grid-template-columns: 1fr;
    }
    .hero-section {
        padding: 6rem 1rem 4rem;
    }
    .section-title {
        font-size: 1.75rem;
    }
}


/* CTA section */

.cta-section {
    background: var(--gradient-primary);
    color: white;
    text-align: center;
    padding: 5rem 0;
}

.cta-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
}

.cta-text {
    font-size: 1.25rem;
    max-width: 700px;
    margin: 0 auto 2rem;
    opacity: 0.9;
}


/* Partner logos */

.partner-logo {
    filter: grayscale(100%);
    opacity: 0.7;
    transition: all 0.3s ease;
    max-width: 150px;
    height: auto;
}

.partner-logo:hover {
    filter: grayscale(0%);
    opacity: 1;
}


/* Approach section */

.approach-item {
    text-align: center;
    padding: 2rem;
}

.approach-icon {
    width: 4rem;
    height: 4rem;
    background-color: rgba(4, 96, 191, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.approach-icon i {
    color: var(--primary-color-1);
    font-size: 1.5rem;
}

.approach-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.approach-text {
    color: var(--text-color-light);
}


/* Typography */

h1,
h2,
h3,
h4,
h5,
h6 {
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1rem;
}

h1 {
    font-size: 3rem;
}

h2 {
    font-size: 2.25rem;
}

h3 {
    font-size: 1.5rem;
}

p {
    margin-bottom: 1rem;
}


/* Sections */

.bg-light {
    background-color: var(--background-color-light);
}


/* Hero Section */

.hero-content {
    max-width: 800px;
    margin: 0 auto;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
}


/* About Section */

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-text h2 {
    text-align: left;
    margin-bottom: 1.5rem;
}

.about-image {
    text-align: center;
}

.rounded-img {
    border-radius: 0.5rem;
    box-shadow: var(--shadow-lg);
    max-width: 100%;
    height: auto;
}

.about-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin-top: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-item h3 {
    color: var(--primary-color-1);
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
}

.stat-item p {
    color: var(--text-color-light);
}


/* Services Section */

.services-grid {

    width: 100%;
    display: grid;
    justify-content: center;
    grid-template-columns: repeat(auto-fit, minmax(250px, auto));
    align-items: center;
    
}

.service-card {
    max-width: 370px;
    margin: 1rem;
    width: 100%;
    padding: 2rem;
    background-color: var(--background-color);
    border-radius: 0.5rem;
    box-shadow: var(--shadow-md);
    transition: transform 0.3s ease;
    text-align: center;
    border: 1px solid var(--border-color);
}

.service-card h3 {
    color: var(--text-color);
}

.service-card p {
    color: var(--text-color-light);
}

.service-icon {
    width: 4rem;
    height: 4rem;
    background-color: rgba(37, 99, 235, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.service-icon i {
    color: var(--primary-color-1);
    font-size: 1.5rem;
}

.service-card:hover {
    transform: translateY(-5px);
}


/* Ventures Section */

.ventures-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.venture-card {
    padding: 2rem;
    background-color: var(--background-color);
    border-radius: 0.5rem;
    box-shadow: var(--shadow-md);
}


/* Partners Section */

.partner-filter-container {
    display: flex;
    justify-content: center;
    margin-bottom: 2rem;
}

.partner-category-selector {
    display: flex;
    background-color: var(--background-color-light);
    padding: 0.25rem;
    border-radius: 9999px;
    gap: 0.25rem;
}

.filter-btn {
    padding: 0.75rem 1.5rem;
    border-radius: 9999px;
    font-weight: 500;
    border: none;
    cursor: pointer;
    white-space: nowrap;
    transition: all 0.3s ease;
    background: transparent;
    color: var(--text-color);
}

.filter-btn:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.filter-btn.active {
    background-color: var(--primary-color-1);
    color: white;
}

.partners-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
}

.partner-logo-card {
    background-color: var(--background-color);
    border-radius: 0.5rem;
    box-shadow: var(--shadow-sm);
    padding: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    filter: grayscale(100%);
    border: 1px solid var(--border-color);
}

.partner-logo-card:hover {
    filter: grayscale(0%);
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

.partner-logo-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
}

.partner-icon-wrapper {
    width: 3rem;
    height: 3rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.partner-icon-wrapper i {
    font-size: 2.5rem;
    color: var(--text-color-light);
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.partner-logo-card:hover .partner-icon-wrapper i {
    opacity: 1;
    color: var(--primary-color-1);
}

.partner-name {
    font-weight: 500;
    color: var(--text-color);
    text-align: center;
}

@media (max-width: 768px) {
    .partner-category-selector {
        flex-wrap: wrap;
        justify-content: center;
        border-radius: 0.5rem;
    }
    .filter-btn {
        flex: 1;
        min-width: 150px;
        text-align: center;
    }
    .partners-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;
    }
}


/* Approach Section */

.approach-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
}

.step {
    text-align: center;
    padding: 2rem;
    background-color: var(--background-color);
    border-radius: 0.5rem;
    box-shadow: var(--shadow-md);
}


/* CTA Section */

.cta {
    text-align: center;
    padding: 5rem 0;
    background-color: var(--primary-color-1);
    color: white;
}

.cta h2 {
    margin-bottom: 1rem;
}

.cta p {
    margin-bottom: 2rem;
}

.cta .btn {
    background-color: white;
    color: var(--primary-color-1);
}

.cta .btn:hover {
    background-color: var(--background-color-light);
}


/* Footer */

.footer {
    background-color: var(--background-color);
    color: var(--background-color);
    padding: 4rem 0 2rem;
}

.footer-content {
    display: grid;
    grid-template-columns: 1.5fr 1fr 1fr 1.5fr;
    gap: 3rem;
    margin-bottom: 3rem;
}

.footer-info {
    color: var(--text-color);
    max-width: 300px;
}

.footer-logo {
    display: inline-block;
    margin-bottom: 1.5rem;
}

.logo-img {
    scale: 1.5;
    height: 50px;
    /* Maintains aspect ratio */
    max-width: 100%;
    /* Prevents image from overflowing the container */
    max-height: 100%;
    /* Prevents image from overflowing the container */
    object-fit: contain;
    width: 150px;
}

.footer-description {
    color: var(--text-color-light);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.footer-social {
    display: flex;
    gap: 1rem;
}

.footer-social a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--text-color);
    transition: all 0.3s ease;
}

.footer-social a:hover {
    background-color: var(--primary-color-1);
    transform: translateY(-3px);
}

.footer-links-column h4 {
    color: var(--text-color-light);
    font-size: 1.125rem;
    margin-bottom: 1.5rem;
}

.footer-links-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links-list li {
    margin-bottom: 0.75rem;
}

.footer-links-list a {
    color: var(--text-color);
    text-decoration: none;
    transition: all 0.3s ease;
    display: inline-block;
}

.footer-links-list a:hover {
    /* color: white; */
    transform: translateX(5px);
}

.footer-newsletter h4 {
    color: var(--text-color-light);
    font-size: 1.125rem;
    margin-bottom: 1.5rem;
}

.footer-newsletter p {
    /* color: rgba(255, 255, 255, 0.8); */
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.newsletter-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.newsletter-input {
    padding: 0.75rem 1rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 0.375rem;
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    width: 100%;
    transition: all 0.3s ease;
}

.newsletter-input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.newsletter-input:focus {
    outline: none;
    border-color: var(--primary-color-1);
    background-color: rgba(255, 255, 255, 0.15);
}

.newsletter-btn {
    width: 100%;
    justify-content: center;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-bottom p {
    color: rgba(255, 255, 255, 0.6);
    margin: 0;
}

@media (max-width: 1024px) {
    .footer-content {
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
    }
    .footer-info {
        max-width: none;
    }
}

@media (max-width: 640px) {
    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    .footer {
        padding: 3rem 0 1.5rem;
    }
    .footer-info,
    .footer-links-column,
    .footer-newsletter {
        text-align: center;
    }
    .footer-social {
        justify-content: center;
    }
    .newsletter-form {
        max-width: 400px;
        margin: 0 auto;
    }
}


/* Process Detail Cards */

.process-detail-card {
    background-color: var(--background-color);
    border-radius: 0.5rem;
    box-shadow: var(--shadow-md);
    margin-bottom: 2rem;
    overflow: hidden;
}

.process-detail-header {
    background-color: var(--primary-color-1);
    color: white;
    padding: 1.5rem;
    display: flex;
    align-items: center;
}

.process-detail-number {
    width: 3rem;
    height: 3rem;
    background-color: white;
    color: var(--primary-color-1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.5rem;
    margin-right: 1.5rem;
}

.process-detail-header h3 {
    margin: 0;
    font-size: 1.75rem;
}

.process-detail-content {
    padding: 2rem;
}

.process-detail-content ul {
    margin: 1.5rem 0;
    padding-left: 1.5rem;
}

.process-detail-content li {
    margin-bottom: 0.75rem;
}


/* Success Stories */

.success-stories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.success-story-card {
    border-radius: 0.5rem;
    overflow: hidden;
    background-color: var(--background-color);
    box-shadow: var(--shadow-md);
    transition: transform 0.3s ease;
}

.success-story-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.success-story-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.success-story-content {
    padding: 1.5rem;
}

.success-story-stats {
    display: flex;
    justify-content: space-between;
    margin-top: 1rem;
    color: var(--text-color-light);
    font-size: 0.875rem;
}

.success-story-stats i {
    margin-right: 0.5rem;
    color: var(--primary-color-1);
}


/* Relative positioning utilities */

.relative {
    position: relative;
}

.mt-6 {
    margin-top: 1.5rem;
}

.mt-20 {
    margin-top: 5rem;
}

.text-center {
    text-align: center;
}


/* Responsive Design */

@media (max-width: 768px) {
    h1 {
        font-size: 2.5rem;
    }
    h2 {
        font-size: 2rem;
    }
    .about-content {
        grid-template-columns: 1fr;
    }
    .about-stats {
        grid-template-columns: 1fr;
    }
    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    .footer-links {
        grid-template-columns: 1fr;
    }
    .hero-buttons {
        flex-direction: column;
    }
    .process-detail-header {
        flex-direction: column;
        text-align: center;
    }
    .process-detail-number {
        margin-right: 0;
        margin-bottom: 1rem;
    }
}


/* Text styles */

p {
    color: var(--text-color);
}

.hero-text {
    color: white !important;
    opacity: 0.9;
}

.about-description {
    color: var(--text-color);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.cta-text {
    color: white;
    opacity: 0.9;
    font-size: 1.25rem;
    margin-bottom: 2rem;
}

.section-subtitle {
    color: var(--text-color-light);
    font-size: 1.25rem;
    max-width: 800px;
    margin: 0 auto 3rem;
    text-align: center;
}