// Blog posts data structure
const blogPosts = {
    posts: [],
    categories: new Set(),
    tags: new Set(),
    authors: new Set()
};

// Function to parse frontmatter from markdown content
function parseFrontmatter(content) {
    const frontmatterRegex = /^---\n([\s\S]*?)\n---/;
    const match = content.match(frontmatterRegex);

    if (!match) return null;

    const frontmatter = match[1];
    const metadata = {};

    frontmatter.split('\n').forEach(line => {
        const [key, ...value] = line.split(':');
        if (key && value.length) {
            metadata[key.trim()] = value.join(':').trim().replace(/^["']|["']$/g, '');
        }
    });

    return metadata;
}

async function loadBlogPosts() {
    try {
        // Fetch the list of blog filenames
        const fileListResponse = await fetch('../../blog-posts/index.json');
        if (!fileListResponse.ok) {
            console.error('Failed to fetch blog post index:', fileListResponse.statusText);
            return null;
        }

        const filenames = await fileListResponse.json();

        // Handle empty filenames array
        if (!filenames || filenames.length === 0) {
            console.log('No blog posts found in index');
            return { posts: [], categories: new Set(), tags: new Set(), authors: new Set() };
        }

        // Fetch and parse all blog posts
        const posts = await Promise.all(filenames.map(async (filename) => {
            try {
                const res = await fetch(`../../blog-posts/${filename}`);
                if (!res.ok) {
                    console.error(`Failed to fetch blog post ${filename}:`, res.statusText);
                    return null;
                }

                const text = await res.text();
                const { content, data } = parseMarkdown(text);

                return {
                    ...data,
                    content,
                    slug: filename.replace('.md', '')
                };
            } catch (error) {
                console.error(`Error processing blog post ${filename}:`, error);
                return null;
            }
        }));

        // Initialize global `blogPosts` object if not already
        // Filter out null posts
        blogPosts.posts = posts.filter(post => post !== null);
        blogPosts.categories = new Set();
        blogPosts.tags = new Set();
        blogPosts.authors = new Set();

        // Function to clean up metadata fields (remove extra quotes and trim spaces)
        const cleanUp = (value) => {
            if (typeof value === 'string') {
                return value.replace(/["\s]/g, ''); // Remove all quotation marks and spaces
            }
            return value;
        };

        // Extract categories, tags, and authors
        blogPosts.posts.forEach(post => {
            // Clean up the metadata fields
            post.title = post.title;
            post.author = post.author;
            post.category = post.category;
            post.summary = post.summary;
            post.canonical_url = post.canonical_url;
            post.linkedin_excerpt = post.linkedin_excerpt;
            post.cover_image = post.cover_image;
            post.release_date = post.release_date;
            post.reading_time = post.reading_time;
            post.draft = post.draft;

            // Clean up tags if they are present
            if (post.tags && Array.isArray(post.tags)) {
                post.tags = post.tags.map(tag => (tag));
                // Add tags to the global set
                post.tags.forEach(tag => blogPosts.tags.add(tag));
            }

            // Add to global sets (category, author)
            if (post.category) blogPosts.categories.add(post.category);
            if (post.author) blogPosts.authors.add(post.author);
        });

        // Sort posts by date
        blogPosts.posts.sort((a, b) => new Date(b.release_date) - new Date(a.release_date));

        return blogPosts;
    } catch (error) {
        console.error('Error loading blog posts:', error);
        return null;
    }
}

// Function to get featured posts
function getFeaturedPosts(limit = 3) {
    return blogPosts.posts
        .filter(post => !post.draft)
        .slice(0, limit);
}

// Function to get posts by category
function getPostsByCategory(category) {
    return blogPosts.posts.filter(post =>
        post.category === category && !post.draft
    );
}

// Function to get posts by tag
function getPostsByTag(tag) {
    return blogPosts.posts.filter(post =>
        post.tags.includes(tag) && !post.draft
    );
}

// Function to get posts by author
function getPostsByAuthor(author) {
    return blogPosts.posts.filter(post =>
        post.author === author && !post.draft
    );
}

// Function to search posts
function searchPosts(query) {
    if (!query) return blogPosts.posts;

    const searchTerms = query.toLowerCase().split(' ');
    return blogPosts.posts.filter(post => {
        const searchableText = `
            ${post.title}
            ${post.summary}
            ${post.category}
            ${post.tags.join(' ')}
            ${post.author}
        `.toLowerCase();

        return searchTerms.every(term => searchableText.includes(term));
    });
}

// Function to get related posts
function getRelatedPosts(currentPost, limit = 3) {
    return blogPosts.posts
        .filter(post =>
            post.slug !== currentPost.slug &&
            !post.draft &&
            (post.category === currentPost.category ||
             post.tags.some(tag => currentPost.tags.includes(tag)))
        )
        .slice(0, limit);
}

// Function to get recent posts
function getRecentPosts(limit = 5) {
    return blogPosts.posts
        .filter(post => !post.draft)
        .slice(0, limit);
}

// Function to get popular tags
function getPopularTags(limit = 10) {
    const tagCounts = new Map();

    blogPosts.posts.forEach(post => {
        post.tags.forEach(tag => {
            tagCounts.set(tag, (tagCounts.get(tag) || 0) + 1);
        });
    });

    return Array.from(tagCounts.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, limit)
        .map(([tag, count]) => ({ tag, count }));
}

// Function to render post card
function renderPostCard(post) {
    // Handle undefined or null post
    if (!post) {
        return `
            <div class="bg-white dark:bg-dark-alt rounded-lg shadow-sm overflow-hidden flex flex-col h-full">
                <div class="p-6 text-center flex-grow">
                    <div class="bg-gray-100 dark:bg-gray-800 rounded-lg p-8 mb-4">
                        <i class="ri-file-damage-line text-4xl text-gray-400"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-2">Post Not Available</h3>
                    <p class="text-gray-600 dark:text-gray-400">This post is currently unavailable.</p>
                </div>
            </div>
        `;
    }

    // Ensure all post properties have default values to prevent "undefined"
    const safePost = {
        title: post.title || 'Untitled Post',
        author: post.author || 'Unknown Author',
        release_date: post.release_date || new Date().toISOString().split('T')[0],
        category: post.category || 'Uncategorized',
        tags: Array.isArray(post.tags) ? post.tags : [],
        summary: post.summary || 'No summary available',
        cover_image: post.cover_image || 'blog-placeholder.webp',
        slug: post.slug || '',
        reading_time: post.reading_time || 5
    };

    // Format date
    const formattedDate = new Date(safePost.release_date).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });

    // Limit summary length
    const truncatedSummary = safePost.summary.length > 120
        ? safePost.summary.substring(0, 120) + '...'
        : safePost.summary;

    return `
        <div class="bg-white dark:bg-dark-alt rounded-lg shadow-sm overflow-hidden flex flex-col h-full transform transition-all duration-300 hover:-translate-y-1 hover:shadow-md">
            <div class="relative overflow-hidden h-48">
                <img
                    src="../assets/images/blog/${safePost.cover_image}"
                    alt="${safePost.title}"
                    onerror="this.src='../assets/images/blog-placeholder.webp'"
                    class="w-full h-full object-cover transition-transform duration-500 hover:scale-105"
                >
                <div class="absolute top-4 left-4">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-primary-600 text-white">
                        ${safePost.category}
                    </span>
                </div>
            </div>

            <div class="p-6 flex-grow flex flex-col">
                <div class="flex items-center text-xs text-gray-500 dark:text-gray-400 mb-3">
                    <span class="flex items-center">
                        <i class="ri-calendar-line mr-1"></i> ${formattedDate}
                    </span>
                    <span class="mx-2">•</span>
                    <span class="flex items-center">
                        <i class="ri-time-line mr-1"></i> ${safePost.reading_time} min read
                    </span>
                </div>

                <h3 class="text-lg font-semibold mb-3 line-clamp-2 hover:text-primary-600 transition-colors">
                    <a href="./blog-post.html?post=${safePost.slug}">${safePost.title}</a>
                </h3>

                <p class="text-gray-600 dark:text-gray-400 text-sm mb-4 flex-grow">
                    ${truncatedSummary}
                </p>

                <div class="mt-auto">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center text-gray-600 dark:text-gray-300 mr-2">
                                <i class="ri-user-line"></i>
                            </div>
                            <span class="text-sm font-medium">${safePost.author}</span>
                        </div>

                        <a href="./blog-post.html?post=${safePost.slug}" class="text-primary-600 hover:text-primary-700 text-sm font-medium flex items-center">
                            Read more <i class="ri-arrow-right-line ml-1"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    `;
}


function renderCategoryCard(category) {
    const postCount = getPostsByCategory(category).length;

    return `
        <button class="category-filter-btn flex items-center justify-between w-full bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-sm font-medium hover:bg-gray-200 transition"
                style="background-color: var(--background-color-light); color: var(--text-color);"
                data-category="${category}">
            <span>${category}</span>
            <span class="ml-2 inline-flex items-center justify-center w-6 h-6 text-xs font-semibold text-gray-800 bg-gray-300 rounded-full">
                ${postCount}
            </span>
        </button>
    `;
}


// Initialize blog functionality
document.addEventListener('DOMContentLoaded', async () => {
    try {
        // Load blog posts
        const result = await loadBlogPosts();
        if (!result) {
            console.error('Failed to load blog posts');
            return;
        }

        // Initialize recent posts
        await displayRecentPosts();
        renderPaginatedBlogPosts();
        // Render featured posts
        const featuredPostsContainer = document.querySelector('.featured-posts .grid');
        if (featuredPostsContainer) {
            const featuredPosts = getFeaturedPosts(3);
            if (featuredPosts.length === 0) {
                featuredPostsContainer.innerHTML = `
                    <div class="col-span-full text-center p-8">
                        <h3 class="text-xl font-semibold mb-2">No Posts Found</h3>
                        <p>There are currently no posts available. Please check back later.</p>
                    </div>
                `;
            } else {
                featuredPostsContainer.innerHTML = featuredPosts.map(renderPostCard).join('');
            }
        }

        // Render categories
        const categoriesContainer = document.querySelector('.blog-categories .category');
        if (categoriesContainer) {
            // Add "All Posts" button at the beginning
            categoriesContainer.innerHTML = `
                <button class="category-filter-btn active flex items-center justify-between w-full bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-sm font-medium hover:bg-gray-200 transition"
                        style="background-color: var(--background-color-light); color: var(--text-color);"
                        data-category="all">
                    <span>All Posts</span>
                    <span class="ml-2 inline-flex items-center justify-center w-6 h-6 text-xs font-semibold text-gray-800 bg-gray-300 rounded-full">
                        ${blogPosts.posts.length}
                    </span>
                </button>
            ` + Array.from(blogPosts.categories)
                .map(renderCategoryCard)
                .join('');

            // Add event listeners to category filter buttons
            document.querySelectorAll('.category-filter-btn').forEach(button => {
                button.addEventListener('click', () => {
                    // Remove active class from all buttons
                    document.querySelectorAll('.category-filter-btn').forEach(btn => {
                        btn.classList.remove('active');
                    });

                    // Add active class to clicked button
                    button.classList.add('active');

                    const category = button.getAttribute('data-category');
                    const postsContainer = document.querySelector('.blog-posts');

                    if (postsContainer) {
                        if (category === 'all') {
                            // Show all posts
                            renderPaginatedBlogPosts();
                        } else {
                            // Filter by category
                            const filteredPosts = getPostsByCategory(category);
                            postsContainer.innerHTML = filteredPosts.map(renderPostCard).join('');
                        }
                    }
                });
            });
        }

        // Initialize search functionality
        const searchInput = document.querySelector('.search-box input');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                const query = e.target.value.trim();
                const searchResults = searchPosts(query);
                const postsContainer = document.querySelector('.blog-posts');
                if (postsContainer) {
                    postsContainer.innerHTML = searchResults.map(renderPostCard).join('');
                }
            });
        }

        // Initialize tag cloud
        const tagCloudContainer = document.querySelector('.tag-cloud .flex');
        if (tagCloudContainer) {
            const popularTags = getPopularTags();
            tagCloudContainer.innerHTML = popularTags.map(({ tag, count }) => `
                <button class="tag-filter-btn" data-tag="${tag}">
                    ${tag}
                </button>
            `).join('');

            // Add event listeners to tag filter buttons
            document.querySelectorAll('.tag-filter-btn').forEach(button => {
                button.addEventListener('click', () => {
                    const tag = button.getAttribute('data-tag');
                    const filteredPosts = getPostsByTag(tag);
                    const postsContainer = document.querySelector('.blog-posts');
                    if (postsContainer) {
                        postsContainer.innerHTML = filteredPosts.map(renderPostCard).join('');
                    }
                });
            });
        }

        // Render all posts by default
        await renderPaginatedBlogPosts();
    } catch (error) {
        console.error('Error initializing blog functionality:', error);
    }
});


// Function to parse markdown content
function parseMarkdown(markdown) {
    try {
        const frontmatterRegex = /---\n([\s\S]+?)\n---/m;
        const match = frontmatterRegex.exec(markdown);

        if (!match) {
            console.error('No frontmatter found in markdown');
            return {
                content: markdown,
                data: {
                    title: 'Untitled Post',
                    author: 'Unknown Author',
                    release_date: new Date().toISOString().split('T')[0],
                    category: 'Uncategorized',
                    tags: [],
                    summary: 'No summary available',
                    cover_image: 'blog-placeholder.webp'
                }
            };
        }

        const frontmatter = match[1];
        const content = markdown.replace(frontmatterRegex, '').trim();

        // Check if frontmatter is in JSON format
        if (frontmatter.trim().startsWith('{') && frontmatter.trim().endsWith('}')) {
            try {
                // Try to parse as JSON
                const jsonData = JSON.parse(frontmatter);
                return { content, data: jsonData };
            } catch (e) {
                console.error('Failed to parse JSON frontmatter:', e);
                // Fall back to line-by-line parsing
            }
        }

        // Line-by-line parsing as fallback
        const data = frontmatter.split('\n').reduce((acc, line) => {
            if (!line.trim()) return acc; // skip empty lines

            const [key, ...rest] = line.split(':');
            if (!key || !rest.length) return acc;

            const rawValue = rest.join(':').trimStart(); // only trim start to preserve intentional spacing

            // Remove trailing commas without trimming the rest
            let value = rawValue.endsWith(',') ? rawValue.slice(0, -1) : rawValue;

            // Parse JSON-like arrays
            if (value.startsWith('[') && value.endsWith(']')) {
                try {
                    acc[key.trim()] = JSON.parse(value);
                } catch {
                    acc[key.trim()] = value;
                }
            } else if (value === 'true' || value === 'false') {
                acc[key.trim()] = value === 'true';
            } else if (!isNaN(value) && !isNaN(parseFloat(value))) {
                acc[key.trim()] = Number(value);
            } else {
                // Preserve whitespace inside strings, just remove wrapping quotes
                if ((value.startsWith('"') && value.endsWith('"')) ||
                    (value.startsWith("'") && value.endsWith("'"))) {
                    value = value.slice(1, -1);
                }
                acc[key.trim()] = value;
            }

            return acc;
        }, {});

        return { content, data };
    } catch (error) {
        console.error('Error parsing markdown:', error);
        return {
            content: markdown,
            data: {
                title: 'Error Parsing Post',
                author: 'System',
                release_date: new Date().toISOString().split('T')[0],
                category: 'Error',
                tags: [],
                summary: 'There was an error parsing this post.',
                cover_image: 'blog-placeholder.webp'
            }
        };
    }
}


// Function to render a single blog post
async function renderBlogPost() {
    const params = new URLSearchParams(window.location.search);
    const slug = params.get('post');
    if (!slug) return;

    const response = await fetch(`/blog-posts/${slug}.md`);
    const markdown = await response.text();
    const { content, data } = parseMarkdown(markdown);

    document.querySelector('.post-title').textContent = data.title;
    document.querySelector('.post-content').innerHTML = marked(content);
}

// Initialize blog post rendering if on blog post page
if (window.location.pathname.includes('blog-post.html')) {
    renderBlogPost();
}

// Function to fetch and display recent posts
async function displayRecentPosts() {
    const posts = blogPosts.posts;
    const container = document.querySelector('.recent-posts');

    if (!container) return;

    // Handle empty posts
    if (!posts || posts.length === 0) {
        container.innerHTML = `
            <div class="text-center p-4">
                <p class="text-sm text-gray-500">No recent posts available.</p>
            </div>
        `;
        return;
    }

    const recentPosts = posts.slice(0, 3);

    container.innerHTML = recentPosts.map(post => {
        // Create safe post object with defaults
        const safePost = {
            title: post?.title || 'Untitled Post',
            release_date: post?.release_date || new Date().toISOString().split('T')[0],
            cover_image: post?.cover_image || 'blog-placeholder.webp',
            slug: post?.slug || ''
        };

        return `
            <a href="./blog-post.html?post=${safePost.slug}" class="recent-post flex items-start gap-4 mb-4 no-underline text-inherit">
                <div class="recent-post-image w-[60px] h-[60px] overflow-hidden rounded-md shrink-0">
                    <img src="../assets/images/blog/${safePost.cover_image}" alt="${safePost.title}" class="w-[70px] h-[70px] object-cover" onerror="this.src='/assets/images/blog-placeholder.webp'" />
                </div>
                <div class="recent-post-content text-sm">
                    <h4 class="font-semibold leading-snug line-clamp-2">${safePost.title}</h4>
                    <span class="text-gray-500 text-xs">${safePost.release_date}</span>
                </div>
            </a>
        `;
    }).join('');
}

// Function to render paginated blog posts
async function renderPaginatedBlogPosts(page = 1, postsPerPage = 6) {
    const posts = blogPosts.posts;
    const container = document.querySelector('.blog-posts');
    const paginationContainer = document.querySelector('.pagination');

    // Handle empty posts
    if (!posts || posts.length === 0) {
        container.innerHTML = `
            <div class="col-span-full text-center p-8">
                <h3 class="text-xl font-semibold mb-2">No Posts Found</h3>
                <p>There are currently no posts available. Please check back later.</p>
            </div>
        `;

        // Clear pagination when no posts
        if (paginationContainer) {
            paginationContainer.innerHTML = '';
        }
        return;
    }

    const totalPages = Math.ceil(posts.length / postsPerPage);
    const start = (page - 1) * postsPerPage;
    const end = start + postsPerPage;
    const paginatedPosts = posts.slice(start, end);

    container.innerHTML = paginatedPosts.map(post => renderPostCard(post)).join('');

    // Pagination controls
    if (paginationContainer) {
        paginationContainer.innerHTML = Array.from({ length: totalPages }, (_, i) => `
            <button class="pagination-btn ${i + 1 === page ? 'active' : ''}" data-page="${i + 1}">${i + 1}</button>
        `).join('');
    }

    // Add event listeners to pagination buttons
    document.querySelectorAll('.pagination-btn').forEach(button => {
        button.addEventListener('click', (e) => {
            const page = parseInt(e.target.dataset.page);
            renderPaginatedBlogPosts(page, postsPerPage);
        });
    });
}
