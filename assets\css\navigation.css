/* Header */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background-color: var(--background-color);
    /* border-bottom: 1px solid var(--border-color); */
    transition: all 0.3s ease;
    padding: 1rem 0;
}

.nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
}

.nav-logo {
    display: flex;
    align-items: center;
}

.nav-links {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.nav-link {
    color: var(--text-color);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: var(--primary-color-1);
}

.nav-link::after {
    content: '';
    position: absolute;
    width: 0%;
    height: 2px;
    bottom: -5px;
    left: 50%;
    transform: translateX(-50%);
    background-color: var(--primary-color-1);
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

/* Dropdown menu */
.dropdown {
    position: relative;
}

.dropdown-content {
    position: absolute;
    top: 100%;
    left: 0;
    background-color: var(--background-color);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 0.5rem 0;
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: all 0.3s ease;
    box-shadow: var(--shadow-md);
}

.dropdown:hover .dropdown-content {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-link {
    display: block;
    padding: 0.5rem 1rem;
    color: var(--text-color);
    text-decoration: none;
    transition: all 0.3s ease;
}

.dropdown-link:hover {
    background-color: var(--background-color-light);
    color: var(--primary-color-1);
}

/* Mobile menu */
.mobile-menu-toggle {
    display: none;
}

.mobile-menu-button {
    display: none;
    flex-direction: column;
    justify-content: space-between;
    width: 30px;
    height: 21px;
    cursor: pointer;
}

.mobile-menu-button span {
    display: block;
    height: 3px;
    background-color: var(--text-color);
    border-radius: 3px;
    transition: all 0.3s ease;
}

/* Button in navigation */
.nav-links .btn {
    margin-left: 1rem;
}

/* Service link */
.service-link {
    display: inline-flex;
    align-items: center;
    color: var(--primary-color-1);
    text-decoration: none;
    font-weight: 500;
    margin-top: 1rem;
    transition: all 0.3s ease;
}

.service-link i {
    margin-left: 0.5rem;
    transition: transform 0.3s ease;
}

.service-link:hover {
    color: var(--primary-color-2);
}

.service-link:hover i {
    transform: translateX(3px);
}

/* Responsive styles */
@media (max-width: 768px) {
    .mobile-menu-toggle {
        display: none;
    }

    .mobile-menu-button {
        display: flex;
    }

    .nav-links {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        flex-direction: column;
        background-color: var(--background-color);
        padding: 1rem;
        border-top: 1px solid var(--border-color);
        box-shadow: var(--shadow-md);
    }

    .mobile-menu-toggle:checked ~ .nav-links {
        display: flex;
    }

    .dropdown-content {
        position: static;
        opacity: 1;
        visibility: visible;
        transform: none;
        border: none;
        box-shadow: none;
        padding: 0;
        margin-top: 0.5rem;
        background-color: var(--background-color-light);
    }

    .dropdown-link {
        padding-left: 2rem;
    }

    .nav-link::after {
        display: none;
    }
    
    .nav-links .btn {
        margin-left: 0;
        width: 100%;
        text-align: center;
    }
} 