<?php
class BlogPost {
    private static $postsDir = __DIR__ . '/../blog-posts/';
    private static $indexFile = __DIR__ . '/../blog-posts/index.json';

    public static function init() {
        if (!file_exists(self::$indexFile)) {
            // Create index file if it doesn't exist
            file_put_contents(self::$indexFile, json_encode([], JSON_PRETTY_PRINT));
        }
    }

    private static function toYAML($frontmatter) {
        $yaml = "";
        foreach ($frontmatter as $key => $value) {
            if (is_array($value)) {
                // Handle empty arrays
                if (empty($value)) {
                    $yaml .= "$key: []\n";
                    continue;
                }

                // Write array as YAML list
                $yaml .= "$key: [" . implode(', ', array_map(function($v) {
                    // Quote all values for consistency
                    if (is_string($v)) {
                        // Escape quotes and special characters
                        $escaped = str_replace('"', '\\"', $v);
                        return '"' . $escaped . '"';
                    } else if (is_bool($v)) {
                        return $v ? 'true' : 'false';
                    } else {
                        return $v;
                    }
                }, $value)) . "]\n";
            } else if (is_bool($value)) {
                $yaml .= "$key: " . ($value ? 'true' : 'false') . "\n";
            } else if (is_string($value)) {
                // Check if the string needs quoting (contains special characters)
                if (preg_match('/[:#\[\]{},"\'&*!|<>=?%@`]/', $value) || trim($value) !== $value) {
                    // Escape quotes
                    $escaped = str_replace('"', '\\"', $value);
                    $yaml .= "$key: \"$escaped\"\n";
                } else {
                    $yaml .= "$key: $value\n";
                }
            } else {
                $yaml .= "$key: $value\n";
            }
        }
        return $yaml;
    }

    public static function createPost($data) {
        try {
            // Log the incoming data for debugging
            error_log('Creating post with data: ' . print_r($data, true));

            // Generate slug from title
            $slug = self::generateSlug($data['title']);
            $filename = $slug . '.md';

            // Ensure tags is an array
            if (isset($data['tags']) && !is_array($data['tags'])) {
                if (is_string($data['tags'])) {
                    $data['tags'] = array_map('trim', explode(',', $data['tags']));
                } else {
                    $data['tags'] = [];
                }
            }

            // Ensure release_date is in the correct format (YYYY-MM-DD)
            if (isset($data['release_date'])) {
                // Try to parse the date
                $timestamp = strtotime($data['release_date']);
                if ($timestamp) {
                    $data['release_date'] = date('Y-m-d', $timestamp);
                } else {
                    $data['release_date'] = date('Y-m-d'); // Default to today
                }
            } else {
                $data['release_date'] = date('Y-m-d'); // Default to today
            }

            // Prepare frontmatter
            $frontmatter = [
                'title' => $data['title'],
                'author' => $data['author'],
                'release_date' => $data['release_date'],
                'category' => $data['category'],
                'tags' => $data['tags'] ?? [],
                'summary' => $data['summary'] ?? '',
                'cover_image' => !empty($data['cover_image']) ? $data['cover_image'] : 'blog-placeholder.webp',
                'reading_time' => isset($data['reading_time']) ? (int)$data['reading_time'] : 5,
                'draft' => isset($data['draft']) ? (bool)$data['draft'] : false
            ];

            // Create markdown content with YAML frontmatter
            $yaml = self::toYAML($frontmatter);
            $content = "---\n$yaml---\n\n" . $data['content'];

            // Check if posts directory exists, create if not
            if (!file_exists(self::$postsDir)) {
                error_log('Creating posts directory: ' . self::$postsDir);
                if (!mkdir(self::$postsDir, 0777, true)) {
                    error_log('Failed to create posts directory: ' . self::$postsDir);
                    return false;
                }
            }

            // Check if index file exists, create if not
            if (!file_exists(self::$indexFile)) {
                error_log('Creating index file: ' . self::$indexFile);
                if (!file_put_contents(self::$indexFile, json_encode([], JSON_PRETTY_PRINT))) {
                    error_log('Failed to create index file: ' . self::$indexFile);
                    return false;
                }
            }

            // Save the post
            $filepath = self::$postsDir . $filename;
            error_log('Saving post to: ' . $filepath);

            if (file_put_contents($filepath, $content)) {
                // Update index
                error_log('Post file created, updating index');
                self::updateIndex($filename);
                error_log('Post created successfully: ' . $filename);
                return true;
            }

            error_log('Failed to write post file: ' . $filepath);
            return false;
        } catch (Exception $e) {
            error_log('Error creating post: ' . $e->getMessage());
            return false;
        }
    }

    public static function updatePost($slug, $data) {
        try {
            // Log the incoming data for debugging
            error_log('Updating post with slug: ' . $slug . ', data: ' . print_r($data, true));

            $filename = $slug . '.md';
            $filepath = self::$postsDir . $filename;

            if (!file_exists($filepath)) {
                error_log('Post file not found for update: ' . $filepath);
                return false;
            }

            // Ensure tags is an array
            if (isset($data['tags']) && !is_array($data['tags'])) {
                if (is_string($data['tags'])) {
                    $data['tags'] = array_map('trim', explode(',', $data['tags']));
                } else {
                    $data['tags'] = [];
                }
            }

            // Ensure release_date is in the correct format (YYYY-MM-DD)
            if (isset($data['release_date'])) {
                // Try to parse the date
                $timestamp = strtotime($data['release_date']);
                if ($timestamp) {
                    $data['release_date'] = date('Y-m-d', $timestamp);
                }
            }

            // Get existing frontmatter
            $content = file_get_contents($filepath);
            if ($content === false) {
                error_log('Failed to read post file for update: ' . $filepath);
                return false;
            }

            // This pattern handles both standard YAML frontmatter and JSON frontmatter
            if (!preg_match('/^---\s*\n([\s\S]*?)\n\s*---/m', $content, $matches)) {
                error_log('Invalid frontmatter format in post file: ' . $filepath);
                return false;
            }

            error_log('Frontmatter found in post for update: ' . $filepath);

            $frontmatter = self::parseYAML($matches[1]);

            // Update frontmatter with new data
            $frontmatter = array_merge($frontmatter, [
                'title' => $data['title'] ?? $frontmatter['title'],
                'author' => $data['author'] ?? $frontmatter['author'],
                'release_date' => $data['release_date'] ?? $frontmatter['release_date'],
                'category' => $data['category'] ?? $frontmatter['category'],
                'tags' => $data['tags'] ?? $frontmatter['tags'],
                'summary' => $data['summary'] ?? $frontmatter['summary'] ?? '',
                'cover_image' => !empty($data['cover_image']) ? $data['cover_image'] : ($frontmatter['cover_image'] ?? 'blog-placeholder.webp'),
                'reading_time' => isset($data['reading_time']) ? (int)$data['reading_time'] : ($frontmatter['reading_time'] ?? 5),
                'draft' => isset($data['draft']) ? (bool)$data['draft'] : ($frontmatter['draft'] ?? false)
            ]);

            // Get the content part (everything after frontmatter)
            $oldContent = preg_replace('/^---\n[\s\S]*?\n---\n/', '', $content);

            // Use new content if provided, otherwise keep the old content
            $postContent = isset($data['content']) ? $data['content'] : $oldContent;

            // Create new content with updated YAML frontmatter
            $yaml = self::toYAML($frontmatter);
            $newContent = "---\n$yaml---\n\n" . $postContent;

            $result = file_put_contents($filepath, $newContent);
            if ($result === false) {
                error_log('Failed to write updated post file: ' . $filepath);
                return false;
            }

            error_log('Post updated successfully: ' . $filename);
            return true;
        } catch (Exception $e) {
            error_log('Error updating post: ' . $e->getMessage());
            return false;
        }
    }

    // YAML/JSON parser for frontmatter
    private static function parseYAML($yaml) {
        // Log the input for debugging
        error_log('Parsing frontmatter: ' . substr($yaml, 0, 100) . (strlen($yaml) > 100 ? '...' : ''));

        // Check if the YAML is in JSON format (starts with '{')
        $yaml = trim($yaml);
        if (substr($yaml, 0, 1) === '{' && substr($yaml, -1) === '}') {
            // Try to parse as JSON
            try {
                // Fix common JSON formatting issues in the frontmatter
                $jsonStr = preg_replace('/([a-zA-Z0-9_]+):\s*/i', '"$1": ', $yaml);
                // Fix array values
                $jsonStr = preg_replace('/:\s*\[(.*?)\]/s', ': [$1]', $jsonStr);
                // Fix unquoted string values
                $jsonStr = preg_replace('/"([^"]+)":\s*([^",\{\}\[\]]+)([,\}\]])/i', '"$1": "$2"$3', $jsonStr);

                error_log('Processed JSON: ' . $jsonStr);

                $data = json_decode($jsonStr, true);
                if ($data !== null) {
                    error_log('Successfully parsed as JSON');
                    return $data;
                } else {
                    error_log('JSON decode error: ' . json_last_error_msg());
                }
            } catch (Exception $e) {
                error_log('JSON parse error: ' . $e->getMessage());
                // Fall back to line-by-line parsing if JSON parsing fails
            }
        }

        // Try to parse as YAML
        try {
            // Remove any quotes around values
            $yaml = preg_replace('/"([^"]+)":\s*"([^"]+)"/', '$1: $2', $yaml);

            // Fall back to line-by-line parsing
            $lines = explode("\n", $yaml);
            $data = [];
            foreach ($lines as $line) {
                // Skip empty lines
                if (trim($line) === '') continue;

                // Match key-value pairs
                if (preg_match('/^([a-zA-Z0-9_]+):\s*(.*)$/', $line, $m)) {
                    $key = $m[1];
                    $val = trim($m[2]);

                    // Remove quotes from the value
                    $val = trim($val, '"\'');

                    if (preg_match('/^\[(.*)\]$/', $val, $arrMatch)) {
                        // Parse array
                        $arrContent = $arrMatch[1];
                        // Handle quoted array items
                        preg_match_all('/"([^"]*)"/', $arrContent, $matches);
                        if (!empty($matches[1])) {
                            $data[$key] = $matches[1];
                        } else {
                            // Handle unquoted or single-quoted array items
                            $arr = array_map(function($v) {
                                return trim(trim($v), '"\'');
                            }, explode(',', $arrContent));
                            $data[$key] = $arr;
                        }
                    } else if ($val === 'true' || $val === 'false') {
                        $data[$key] = $val === 'true';
                    } else if (is_numeric($val)) {
                        $data[$key] = $val + 0; // Convert to number
                    } else {
                        $data[$key] = $val;
                    }
                }
            }

            if (!empty($data)) {
                error_log('Successfully parsed as YAML');
                return $data;
            }
        } catch (Exception $e) {
            error_log('YAML parse error: ' . $e->getMessage());
        }

        error_log('Failed to parse frontmatter');
        return [];
    }

    public static function deletePost($slug) {
        $filename = $slug . '.md';
        $filepath = self::$postsDir . $filename;

        if (file_exists($filepath) && unlink($filepath)) {
            // Update index
            self::updateIndex($filename, true);
            return true;
        }
        return false;
    }

    public static function getPost($slug) {
        $filename = $slug . '.md';
        $filepath = self::$postsDir . $filename;

        if (!file_exists($filepath)) {
            error_log('Post file not found: ' . $filepath);
            return null;
        }

        try {
            $content = file_get_contents($filepath);
            if ($content === false) {
                error_log('Failed to read post file: ' . $filepath);
                return null;
            }

            // Match the frontmatter between --- markers
            // This pattern handles both standard YAML frontmatter and JSON frontmatter
            if (preg_match('/^---\s*\n([\s\S]*?)\n\s*---/m', $content, $matches)) {
                error_log('Frontmatter found in post: ' . $filepath);
                $frontmatter = self::parseYAML($matches[1]);
                $content = preg_replace('/^---\s*\n[\s\S]*?\n\s*---\s*\n/', '', $content);

                return [
                    'slug' => $slug,
                    'frontmatter' => $frontmatter,
                    'content' => $content
                ];
            } else {
                error_log('Invalid frontmatter format in post: ' . $filepath);
                return null;
            }
        } catch (Exception $e) {
            error_log('Error processing post ' . $filepath . ': ' . $e->getMessage());
            return null;
        }
    }

    public static function getAllPosts() {
        $posts = [];
        $files = glob(self::$postsDir . '*.md');

        error_log('Files: ' . print_r($files, true));

        if (empty($files)) {
            error_log('No blog post files found in directory: ' . self::$postsDir);
            return $posts;
        }

        foreach ($files as $file) {
            try {
                $content = file_get_contents($file);
                if ($content === false) {
                    error_log('Failed to read file: ' . $file);
                    continue;
                }

                // Match the frontmatter between --- markers
                // This pattern handles both standard YAML frontmatter and JSON frontmatter
                if (preg_match('/^---\s*\n([\s\S]*?)\n\s*---/m', $content, $matches)) {
                    error_log('Frontmatter found in file: ' . $file);
                    $frontmatter = self::parseYAML($matches[1]);
                    $slug = basename($file, '.md');

                    // Provide default values for required fields if missing
                    if (empty($frontmatter['title'])) {
                        error_log('Missing title in post: ' . $file);
                        $frontmatter['title'] = 'Untitled Post';
                    }

                    if (empty($frontmatter['author'])) {
                        error_log('Missing author in post: ' . $file);
                        $frontmatter['author'] = 'Unknown Author';
                    }

                    // Add other default values
                    if (empty($frontmatter['category'])) {
                        $frontmatter['category'] = 'Uncategorized';
                    }

                    if (empty($frontmatter['release_date'])) {
                        $frontmatter['release_date'] = date('Y-m-d');
                    }

                    if (!isset($frontmatter['tags']) || !is_array($frontmatter['tags'])) {
                        $frontmatter['tags'] = [];
                    }

                    $posts[] = [
                        'slug' => $slug,
                        'frontmatter' => $frontmatter
                    ];
                } else {
                    error_log('Invalid frontmatter format in file: ' . $file);
                }
            } catch (Exception $e) {
                error_log('Error processing file ' . $file . ': ' . $e->getMessage());
            }
        }

        // Sort by release date if posts exist
        if (!empty($posts)) {
            usort($posts, function($a, $b) {
                $dateA = isset($a['frontmatter']['release_date']) ? strtotime($a['frontmatter']['release_date']) : 0;
                $dateB = isset($b['frontmatter']['release_date']) ? strtotime($b['frontmatter']['release_date']) : 0;
                return $dateB - $dateA;
            });
        }

        return $posts;
    }

    private static function generateSlug($title) {
        // Convert to lowercase and replace spaces with hyphens
        $slug = strtolower($title);
        $slug = preg_replace('/[^a-z0-9-]/', '-', $slug);
        $slug = preg_replace('/-+/', '-', $slug);
        $slug = trim($slug, '-');

        // Add date prefix
        return date('Y-m-d') . '-' . $slug;
    }

    private static function updateIndex($filename, $isDelete = false) {
        try {
            // Check if index file exists, create if not
            if (!file_exists(self::$indexFile)) {
                error_log('Index file does not exist, creating: ' . self::$indexFile);
                file_put_contents(self::$indexFile, json_encode([], JSON_PRETTY_PRINT));
                $index = [];
            } else {
                $indexContent = file_get_contents(self::$indexFile);
                if ($indexContent === false) {
                    error_log('Failed to read index file: ' . self::$indexFile);
                    $index = [];
                } else {
                    $index = json_decode($indexContent, true);
                    if ($index === null) {
                        error_log('Invalid JSON in index file: ' . self::$indexFile);
                        $index = [];
                    }
                }
            }

            if ($isDelete) {
                $index = array_filter($index, fn($f) => $f !== $filename);
            } else {
                if (!in_array($filename, $index)) {
                    $index[] = $filename;
                }
            }

            $result = file_put_contents(self::$indexFile, json_encode($index, JSON_PRETTY_PRINT));
            if ($result === false) {
                error_log('Failed to write index file: ' . self::$indexFile);
            } else {
                error_log('Index file updated successfully');
            }
        } catch (Exception $e) {
            error_log('Error updating index: ' . $e->getMessage());
        }
    }
}