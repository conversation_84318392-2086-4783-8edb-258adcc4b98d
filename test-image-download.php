<?php
/**
 * Test script for the image download functionality
 * This script tests the upload-markdown API with a URL-based cover image
 */

// Test data with a sample image URL
$testData = [
    'title' => 'Test Blog Post with URL Image',
    'author' => 'Test Author',
    'category' => 'Technology',
    'markdown_content' => '# Test Blog Post

This is a test blog post to verify that the image download functionality works correctly.

The cover image should be downloaded from the provided URL and saved to the assets/images/blog directory.',
    'release_date' => date('Y-m-d'),
    'tags' => ['test', 'image-download'],
    'summary' => 'A test post to verify image URL download functionality',
    'cover_image' => 'https://images.pexels.com/photos/8386440/pexels-photo-8386440.jpeg', // Sample image URL
    'reading_time' => 3,
    'draft' => false
];

// Convert to JSON
$jsonData = json_encode($testData, JSON_PRETTY_PRINT);

echo "Test Data to be sent to upload-markdown API:\n";
echo "==========================================\n";
echo $jsonData . "\n\n";

echo "To test this functionality:\n";
echo "1. Save this data to a file (e.g., test-data.json)\n";
echo "2. Use curl or Postman to send a POST request to /cms/api/upload-markdown.php\n";
echo "3. Check the assets/images/blog directory for the downloaded image\n";
echo "4. Check the blog-posts directory for the created markdown file\n\n";

echo "Example curl command:\n";
echo "curl -X POST http://your-domain/cms/api/upload-markdown.php \\\n";
echo "  -H \"Content-Type: application/json\" \\\n";
echo "  -d '" . str_replace("'", "\\'", $jsonData) . "'\n\n";

echo "Expected behavior:\n";
echo "- The image from the URL should be downloaded and saved with a unique filename\n";
echo "- The markdown file should reference the local filename instead of the URL\n";
echo "- If the download fails, it should fallback to 'blog-placeholder.webp'\n";
?>
