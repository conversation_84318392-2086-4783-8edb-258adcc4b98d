<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sahla Blog CMS</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        },
                    },
                    fontFamily: {
                        sans: ['Inter', 'sans-serif'],
                    },
                    boxShadow: {
                        card: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
                    },
                },
            },
        }
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@4.5.0/fonts/remixicon.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/easymde/dist/easymde.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/easymde/dist/easymde.min.css">
    <style>
        /* Custom styles for EasyMDE */
        .EasyMDEContainer {
            border-radius: 0.375rem;
        }
        .EasyMDEContainer .CodeMirror {
            border-radius: 0.375rem;
            border-color: #e5e7eb;
            min-height: 300px;
        }
        .EasyMDEContainer .editor-toolbar {
            border-top-left-radius: 0.375rem;
            border-top-right-radius: 0.375rem;
            border-color: #e5e7eb;
        }
        /* Loading spinner */
        .spinner {
            border: 3px solid rgba(0, 0, 0, 0.1);
            border-radius: 50%;
            border-top: 3px solid #3498db;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            display: inline-block;
            vertical-align: middle;
            margin-right: 8px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="bg-gray-50 font-sans text-gray-800 min-h-screen">
    <!-- Header -->
    <header class="bg-white border-b border-gray-200 sticky top-0 z-10 shadow-sm">
        <div class="container mx-auto px-4 py-4">
            <div class="flex justify-between items-center">
                <h1 class="text-2xl md:text-3xl font-bold text-primary-700">
                    <i class="ri-quill-pen-line mr-2"></i>Sahla Blog CMS
                </h1>
                <button onclick="showNewPostForm()" class="bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700 transition-colors duration-200 flex items-center">
                    <i class="ri-add-line mr-2"></i>New Post
                </button>
            </div>
        </div>
    </header>

    <main class="container mx-auto px-4 py-8">
        <!-- Post List -->
        <div id="postList" class="bg-white rounded-lg shadow-card p-6 mb-8">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-semibold text-gray-800">All Posts</h2>
                <div class="flex items-center">
                    <button onclick="loadPosts()" class="text-primary-600 hover:text-primary-800 flex items-center">
                        <i class="ri-refresh-line mr-1"></i> Refresh
                    </button>
                </div>
            </div>
            <div class="overflow-x-auto rounded-lg border border-gray-200">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Author</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody id="postTableBody" class="bg-white divide-y divide-gray-200">
                        <!-- Loading indicator -->
                        <tr>
                            <td colspan="5" class="px-6 py-4 text-center text-gray-500">
                                <div class="spinner"></div> Loading posts...
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Post Form -->
        <div id="postForm" class="bg-white rounded-lg shadow-card p-6 hidden">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-semibold text-gray-800" id="formTitle">New Post</h2>
                <button type="button" onclick="hidePostForm()" class="text-gray-500 hover:text-gray-700">
                    <i class="ri-close-line text-xl"></i>
                </button>
            </div>

            <form id="blogPostForm" onsubmit="handleSubmit(event)" class="space-y-6">
                <input type="hidden" id="postSlug">

                <!-- Basic Info Section -->
                <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                    <h3 class="text-md font-medium text-gray-700 mb-3">Basic Information</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="title" class="block text-sm font-medium text-gray-700 mb-1">Title *</label>
                            <input type="text" id="title" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        </div>
                        <div>
                            <label for="author" class="block text-sm font-medium text-gray-700 mb-1">Author *</label>
                            <input type="text" id="author" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        </div>
                        <div>
                            <label for="category" class="block text-sm font-medium text-gray-700 mb-1">Category *</label>
                            <input type="text" id="category" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        </div>
                        <div>
                            <label for="releaseDate" class="block text-sm font-medium text-gray-700 mb-1">Release Date *</label>
                            <input type="date" id="releaseDate" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        </div>
                    </div>
                </div>

                <!-- Additional Info Section -->
                <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                    <h3 class="text-md font-medium text-gray-700 mb-3">Additional Information</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-4">
                        <div>
                            <label for="tags" class="block text-sm font-medium text-gray-700 mb-1">Tags (comma-separated)</label>
                            <input type="text" id="tags" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                            <p class="mt-1 text-xs text-gray-500">Example: Technology, AI, Business</p>
                        </div>
                        <div>
                            <label for="coverImage" class="block text-sm font-medium text-gray-700 mb-1">Cover Image</label>
                            <input type="text" id="coverImage" placeholder="blog-placeholder.webp" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                            <p class="mt-1 text-xs text-gray-500">Image filename in assets/images/blog/</p>
                        </div>
                    </div>
                    <div>
                        <label for="summary" class="block text-sm font-medium text-gray-700 mb-1">Summary</label>
                        <textarea id="summary" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"></textarea>
                        <p class="mt-1 text-xs text-gray-500">Brief description of the post (appears in previews)</p>
                    </div>
                </div>

                <!-- Content Section -->
                <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                    <h3 class="text-md font-medium text-gray-700 mb-3">Content *</h3>
                    <div>
                        <textarea id="content"></textarea>
                        <p class="mt-1 text-xs text-gray-500">Use Markdown formatting for your content</p>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="flex justify-end gap-4 pt-4 border-t border-gray-200">
                    <button type="button" onclick="hidePostForm()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        Cancel
                    </button>
                    <button type="submit" id="saveButton" class="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        Save Post
                    </button>
                </div>
            </form>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-white border-t border-gray-200 py-4 mt-8">
        <div class="container mx-auto px-4 text-center text-gray-500 text-sm">
            &copy; 2025 Sahla Smart Solutions. All rights reserved.
        </div>
    </footer>

    <script>
        let editor;
        let currentPost = null;

        // Initialize EasyMDE editor
        document.addEventListener('DOMContentLoaded', () => {
            // Initialize the Markdown editor
            editor = new EasyMDE({
                element: document.getElementById('content'),
                spellChecker: false,
                status: ['lines', 'words'],
                toolbar: [
                    'bold', 'italic', 'heading', '|',
                    'quote', 'unordered-list', 'ordered-list', '|',
                    'link', 'image', 'table', '|',
                    'preview', 'side-by-side', 'fullscreen', '|',
                    'guide'
                ],
                placeholder: 'Write your post content here using Markdown...',
                autofocus: false
            });

            // Set default date to today
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('releaseDate').value = today;

            // Load posts
            loadPosts();
        });

        // Show notification
        function showNotification(message, type = 'success') {
            // Create notification element if it doesn't exist
            let notification = document.getElementById('notification');
            if (!notification) {
                notification = document.createElement('div');
                notification.id = 'notification';
                notification.className = 'fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 transform transition-transform duration-300 translate-x-full';
                document.body.appendChild(notification);
            }

            // Set notification type
            if (type === 'success') {
                notification.className = 'fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 bg-green-100 border-l-4 border-green-500 text-green-700 flex items-center transform transition-transform duration-300';
                notification.innerHTML = `<i class="ri-check-line mr-2 text-xl"></i> ${message}`;
            } else if (type === 'error') {
                notification.className = 'fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 bg-red-100 border-l-4 border-red-500 text-red-700 flex items-center transform transition-transform duration-300';
                notification.innerHTML = `<i class="ri-error-warning-line mr-2 text-xl"></i> ${message}`;
            } else if (type === 'info') {
                notification.className = 'fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 bg-blue-100 border-l-4 border-blue-500 text-blue-700 flex items-center transform transition-transform duration-300';
                notification.innerHTML = `<i class="ri-information-line mr-2 text-xl"></i> ${message}`;
            }

            // Show notification
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 10);

            // Hide notification after 3 seconds
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
            }, 3000);
        }

        // Load all posts
        async function loadPosts() {
            const tbody = document.getElementById('postTableBody');

            // Show loading indicator
            tbody.innerHTML = `
                <tr>
                    <td colspan="5" class="px-6 py-4 text-center text-gray-500">
                        <div class="spinner"></div> Loading posts...
                    </td>
                </tr>
            `;

            try {
                const response = await fetch('/cms/api/posts.php');

                if (!response.ok) {
                    throw new Error(`Server responded with status: ${response.status}`);
                }

                const data = await response.json();

                // Check if the response is an error message
                if (data.error) {
                    // If no posts found, show empty state
                    if (data.error === 'No posts found') {
                        tbody.innerHTML = `
                            <tr>
                                <td colspan="5" class="px-6 py-4 text-center text-gray-500">
                                    <i class="ri-file-list-3-line text-4xl mb-2 block"></i>
                                    <p class="mb-2">No posts found</p>
                                    <button onclick="showNewPostForm()" class="text-primary-600 hover:text-primary-800 font-medium">
                                        Create your first post
                                    </button>
                                </td>
                            </tr>
                        `;
                        return;
                    } else {
                        throw new Error(data.error);
                    }
                }

                // If we have posts, display them
                const posts = data;

                if (!posts || posts.length === 0) {
                    tbody.innerHTML = `
                        <tr>
                            <td colspan="5" class="px-6 py-4 text-center text-gray-500">
                                <i class="ri-file-list-3-line text-4xl mb-2 block"></i>
                                <p class="mb-2">No posts found</p>
                                <button onclick="showNewPostForm()" class="text-primary-600 hover:text-primary-800 font-medium">
                                    Create your first post
                                </button>
                            </td>
                        </tr>
                    `;
                    return;
                }

                tbody.innerHTML = posts.map(post => {
                    // Create safe post object with defaults
                    const frontmatter = post.frontmatter || {};
                    const safePost = {
                        title: frontmatter.title || 'Untitled Post',
                        author: frontmatter.author || 'Unknown Author',
                        category: frontmatter.category || 'Uncategorized',
                        release_date: frontmatter.release_date || 'No date',
                        slug: post.slug || ''
                    };

                    return `
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">${safePost.title}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">${safePost.author}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-primary-100 text-primary-800">
                                    ${safePost.category}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                ${safePost.release_date}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <button onclick="editPost('${safePost.slug}')" class="text-primary-600 hover:text-primary-900 mr-3">
                                    <i class="ri-edit-line"></i> Edit
                                </button>
                                <button onclick="deletePost('${safePost.slug}')" class="text-red-600 hover:text-red-900">
                                    <i class="ri-delete-bin-line"></i> Delete
                                </button>
                            </td>
                        </tr>
                    `;
                }).join('');
            } catch (error) {
                console.error('Error loading posts:', error);
                tbody.innerHTML = `
                    <tr>
                        <td colspan="5" class="px-6 py-4 text-center text-red-500">
                            <i class="ri-error-warning-line text-4xl mb-2 block"></i>
                            <p class="mb-2">Error loading posts: ${error.message}</p>
                            <button onclick="loadPosts()" class="text-primary-600 hover:text-primary-800 font-medium">
                                Try again
                            </button>
                        </td>
                    </tr>
                `;
            }
        }

        // Show new post form
        function showNewPostForm() {
            currentPost = null;
            document.getElementById('formTitle').textContent = 'New Post';
            document.getElementById('blogPostForm').reset();

            // Set default date to today
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('releaseDate').value = today;

            // Clear editor
            editor.value('');

            // Show form
            document.getElementById('postForm').classList.remove('hidden');
            document.getElementById('postList').classList.add('hidden');

            // Focus on title field
            setTimeout(() => {
                document.getElementById('title').focus();
            }, 100);
        }

        // Hide post form
        function hidePostForm() {
            document.getElementById('postForm').classList.add('hidden');
            document.getElementById('postList').classList.remove('hidden');
        }

        // Edit post
        async function editPost(slug) {
            try {
                // Show loading in the form title
                document.getElementById('formTitle').innerHTML = '<div class="spinner"></div> Loading post...';

                // Show form
                document.getElementById('postForm').classList.remove('hidden');
                document.getElementById('postList').classList.add('hidden');

                const response = await fetch(`/cms/api/posts.php?slug=${slug}`);

                if (!response.ok) {
                    throw new Error(`Server responded with status: ${response.status}`);
                }

                const data = await response.json();

                // Check if the response is an error message
                if (data.error) {
                    throw new Error(data.error);
                }

                const post = data;
                currentPost = post;

                // Create safe post object with defaults
                const frontmatter = post.frontmatter || {};

                document.getElementById('formTitle').textContent = 'Edit Post';
                document.getElementById('postSlug').value = post.slug || '';
                document.getElementById('title').value = frontmatter.title || '';
                document.getElementById('author').value = frontmatter.author || '';
                document.getElementById('category').value = frontmatter.category || '';

                // Format date properly
                if (frontmatter.release_date) {
                    try {
                        const date = new Date(frontmatter.release_date);
                        if (!isNaN(date.getTime())) {
                            document.getElementById('releaseDate').value = date.toISOString().split('T')[0];
                        } else {
                            document.getElementById('releaseDate').value = new Date().toISOString().split('T')[0];
                        }
                    } catch (e) {
                        document.getElementById('releaseDate').value = new Date().toISOString().split('T')[0];
                    }
                } else {
                    document.getElementById('releaseDate').value = new Date().toISOString().split('T')[0];
                }

                // Handle tags safely
                const tags = Array.isArray(frontmatter.tags) ? frontmatter.tags.join(', ') : '';
                document.getElementById('tags').value = tags;

                document.getElementById('coverImage').value = frontmatter.cover_image || '';
                document.getElementById('summary').value = frontmatter.summary || '';
                editor.value(post.content || '');
            } catch (error) {
                console.error('Error loading post:', error);
                document.getElementById('formTitle').textContent = 'Error Loading Post';
                showNotification(`Failed to load post: ${error.message}`, 'error');

                // Return to post list after a delay
                setTimeout(() => {
                    hidePostForm();
                }, 2000);
            }
        }

        // Delete post
        async function deletePost(slug) {
            if (!confirm('Are you sure you want to delete this post? This action cannot be undone.')) return;

            try {
                // Show notification
                showNotification('Deleting post...', 'info');

                const response = await fetch(`/cms/api/posts.php?slug=${slug}`, {
                    method: 'DELETE'
                });

                if (!response.ok) {
                    throw new Error(`Server responded with status: ${response.status}`);
                }

                const data = await response.json();

                // Check if the response is an error message
                if (data.error) {
                    throw new Error(data.error);
                }

                // Success - reload posts and show notification
                showNotification('Post deleted successfully', 'success');
                loadPosts();
            } catch (error) {
                console.error('Error deleting post:', error);
                showNotification(`Failed to delete post: ${error.message}`, 'error');
            }
        }

        // Handle form submission
        async function handleSubmit(event) {
            event.preventDefault();

            // Get save button and show loading state
            const saveButton = document.getElementById('saveButton');
            const originalButtonText = saveButton.innerHTML;
            saveButton.innerHTML = '<div class="spinner"></div> Saving...';
            saveButton.disabled = true;

            try {
                // Validate form
                const title = document.getElementById('title').value.trim();
                const author = document.getElementById('author').value.trim();
                const category = document.getElementById('category').value.trim();
                const releaseDate = document.getElementById('releaseDate').value;
                const content = editor.value().trim();

                if (!title || !author || !category || !releaseDate || !content) {
                    throw new Error('Please fill in all required fields');
                }

                // Clean up tags - remove empty tags
                const tagsInput = document.getElementById('tags').value;
                const tags = tagsInput
                    ? tagsInput.split(',')
                        .map(tag => tag.trim())
                        .filter(tag => tag.length > 0)
                    : [];

                // Prepare the data for the API
                const formData = {
                    title,
                    author,
                    category,
                    release_date: releaseDate,
                    tags,
                    cover_image: document.getElementById('coverImage').value.trim() || 'blog-placeholder.webp',
                    summary: document.getElementById('summary').value.trim(),
                    content // just the post body
                };

                const method = currentPost ? 'PUT' : 'POST';
                const baseUrl = '/cms/api/posts.php';
                const url = currentPost ? `${baseUrl}?slug=${currentPost.slug}` : baseUrl;

                const response = await fetch(url, {
                    method,
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });

                if (!response.ok) {
                    throw new Error(`Server responded with status: ${response.status}`);
                }

                const data = await response.json();

                // Check if the response is an error message
                if (data.error) {
                    throw new Error(data.error);
                }

                // Success - hide form, reload posts, and show notification
                const action = currentPost ? 'updated' : 'created';
                showNotification(`Post ${action} successfully`, 'success');
                hidePostForm();
                loadPosts();
            } catch (error) {
                console.error('Error saving post:', error);
                showNotification(`Failed to save post: ${error.message}`, 'error');
            } finally {
                // Reset button state
                saveButton.innerHTML = originalButtonText;
                saveButton.disabled = false;
            }
        }
    </script>
</body>
</html>