<?php
header('Content-Type: application/json');
require_once __DIR__ . '/../BlogPost.php';

// Initialize BlogPost
BlogPost::init();

// Enable error reporting for debugging
ini_set('display_errors', 1);
error_reporting(E_ALL);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../../php_errors.log');

// Log request details
error_log('Request Method: ' . $_SERVER['REQUEST_METHOD']);
error_log('Request URI: ' . $_SERVER['REQUEST_URI']);

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

try {
    // Check if we have a file upload
    if (isset($_FILES['markdown_file']) && $_FILES['markdown_file']['error'] === UPLOAD_ERR_OK) {
        // File upload method
        error_log('Processing file upload');
        
        // Get file content
        $markdownContent = file_get_contents($_FILES['markdown_file']['tmp_name']);
        if ($markdownContent === false) {
            error_log('Failed to read uploaded file');
            http_response_code(500);
            echo json_encode(['error' => 'Failed to read uploaded file']);
            exit;
        }
        
        // Get form data
        $title = $_POST['title'] ?? '';
        $author = $_POST['author'] ?? '';
        $category = $_POST['category'] ?? '';
        $releaseDate = $_POST['release_date'] ?? date('Y-m-d');
        $tags = isset($_POST['tags']) ? explode(',', $_POST['tags']) : [];
        $summary = $_POST['summary'] ?? '';
        $coverImage = $_POST['cover_image'] ?? 'blog-placeholder.webp';
        $readingTime = $_POST['reading_time'] ?? 5;
        $draft = isset($_POST['draft']) ? filter_var($_POST['draft'], FILTER_VALIDATE_BOOLEAN) : false;
        
        // Clean up tags array
        $tags = array_map('trim', $tags);
        $tags = array_filter($tags, function($tag) { return !empty($tag); });
        
    } else {
        // JSON method
        error_log('Processing JSON data');
        
        // Get the raw input
        $rawInput = file_get_contents('php://input');
        error_log('Raw input: ' . $rawInput);

        // Decode JSON
        $data = json_decode($rawInput, true);
        if (!$data) {
            error_log('JSON decode error: ' . json_last_error_msg());
            http_response_code(400);
            echo json_encode(['error' => 'Invalid JSON data: ' . json_last_error_msg()]);
            exit;
        }
        
        // Extract data from JSON
        $title = $data['title'] ?? '';
        $author = $data['author'] ?? '';
        $category = $data['category'] ?? '';
        $markdownContent = $data['markdown_content'] ?? '';
        $releaseDate = $data['release_date'] ?? date('Y-m-d');
        $tags = $data['tags'] ?? [];
        $summary = $data['summary'] ?? '';
        $coverImage = $data['cover_image'] ?? 'blog-placeholder.webp';
        $readingTime = $data['reading_time'] ?? 5;
        $draft = $data['draft'] ?? false;
    }
    
    // Check for required fields
    if (empty($title) || empty($author) || empty($category) || empty($markdownContent)) {
        error_log('Missing required fields');
        http_response_code(400);
        echo json_encode(['error' => 'Missing required fields: title, author, category, and markdown content are required']);
        exit;
    }

    // Prepare the post data
    $postData = [
        'title' => $title,
        'author' => $author,
        'release_date' => $releaseDate,
        'category' => $category,
        'tags' => $tags,
        'summary' => $summary,
        'cover_image' => $coverImage,
        'reading_time' => (int)$readingTime,
        'draft' => $draft,
        'content' => $markdownContent
    ];

    // Create the post
    if (BlogPost::createPost($postData)) {
        http_response_code(201);
        echo json_encode(['message' => 'Markdown file uploaded and processed successfully']);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to create post']);
    }
} catch (Exception $e) {
    error_log('Error processing markdown upload: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Server error: ' . $e->getMessage()]);
}
