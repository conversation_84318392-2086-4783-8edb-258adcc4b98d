RewriteEngine On

# Handle API requests
RewriteRule ^api/posts/?$ api/posts.php [L,QSA]
RewriteRule ^api/posts/([^/]+)/?$ api/posts.php [L,QSA]

# Ensure PHP files are not directly accessible
RewriteCond %{REQUEST_URI} !^/cms/api/
RewriteRule \.php$ - [F]

# Prevent directory listing
Options -Indexes

# Set default character set
AddDefaultCharset UTF-8

# Enable CORS for API
<IfModule mod_headers.c>
    Header set Access-Control-Allow-Origin "*"
    Header set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
    Header set Access-Control-Allow-Headers "Content-Type"
</IfModule> 