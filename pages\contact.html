
<!DOCTYPE html>
<html lang="en">
<head>     <link rel="icon" type="image/x-icon" href="../assets/images/favicon.ico?v=2">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Us - Sahla Smart Solutions</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>tailwind.config={theme:{extend:{colors:{primary:'#0056b3',secondary:'#4dabf7'},borderRadius:{'none':'0px','sm':'4px',DEFAULT:'8px','md':'12px','lg':'16px','xl':'20px','2xl':'24px','3xl':'32px','full':'9999px','button':'8px'}}}}</script>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/navigation.css">    
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@4.5.0/fonts/remixicon.css" rel="stylesheet">
    <style>
        .contact-hero {
            background-image: linear-gradient(to bottom, #111827d8, #22267a8f), url('https://images.pexels.com/photos/8386440/pexels-photo-8386440.jpeg');
            background-size: cover;
            background-position: center;
            padding: 8rem 0 4rem;
        }

        .contact-form {
            background: var(--bg-light);
            border-radius: 1rem;
            padding: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: var(--text-color);
        }

        .form-input {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            background: var(--bg-color);
            color: var(--text-color);
            transition: border-color 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        .form-textarea {
            min-height: 150px;
            resize: vertical;
        }

        .contact-info-card {
            background: var(--bg-light);
            border-radius: 1rem;
            padding: 2rem;
            height: 100%;
            transition: transform 0.3s ease;
        }

        .contact-info-card:hover {
            transform: translateY(-5px);
        }

        .contact-icon {
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--primary-color);
            color: white;
            border-radius: 12px;
            margin-bottom: 1rem;
        }

        .success-message {
            display: none;
            background: #10B981;
            color: white;
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
        }

        .error-message {
            display: none;
            background: #EF4444;
            color: white;
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body class="bg-white">
    <!-- Header & Navigation -->
   <header class="header">
    <div class="container">
      <nav class="nav">
        <a href="../index.html" class="nav-logo">
          <img src="../assets/images/Main Horizontal Version.svg" alt="Sahla Logo" class="logo-img" id="nav-logo">
        </a>
        <input type="checkbox" id="mobile-menu-toggle" class="mobile-menu-toggle">
        <label for="mobile-menu-toggle" class="mobile-menu-button">
          <span></span>
          <span></span>
          <span></span>
        </label>
        <div class="nav-links">
          <div class="dropdown">
            <a href="#services" class="nav-link">Services</a>
            <div class="dropdown-content">
              <a href="./process-automation.html" class="dropdown-link">Process Automation</a>
              <a href="./technology-consulting.html" class="dropdown-link">Technology Consulting</a>
            </div>
          </div>
          <a href="./ventures.html" class="nav-link">Our Ventures</a>
          <a href="./partners.html" class="nav-link">Partners</a>
          <a href="./careers.html" class="nav-link">Careers</a>
          <a href="./blog.html" class="nav-link">Blog</a>
          <a href="./about.html" class="nav-link">About us</a>
          <div class="px-4">
            <div class="h-6 border-l-2 border-gray-300"></div>
          </div>
          <a href="./contact.html" class="px-6 py-2 !rounded-button hover:bg-opacity-90 transition-all" style="background-color: var(--background-color-light); color: var(--text-color);">Contact Sales</a>
          <div class="hidden md:flex items-center gap-4">
            <div class="theme-switch" id="theme-switch">
              <i class="ri-moon-line moon-icon"></i>
              <i class="ri-sun-line sun-icon"></i>
              <script>
                (() => {
                  const savedTheme = localStorage.getItem('theme');
                  const preferred = savedTheme || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                  document.documentElement.setAttribute('data-theme', preferred);
                })();
              </script>
            </div>
          </div>
        </div>
      </nav>
    </div>
  </header>

    <!-- Contact Hero Section -->
    <section class="contact-hero">
        <div class="container">
            <div class="text-center text-white">
                <h1 class="h1 mb-4" style="margin-top: 10%;">Get in Touch</h1>
                <p class="text-xl max-w-2xl" style="text-align: start; color: #e5e7eb;">Ready to transform your business? Let's discuss how we can help you achieve your goals through venture building or automation solutions.</p>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="section">
        <div class="container">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Contact Information -->
                <div class="lg:col-span-1">
                    <div class="contact-info-card">
                        <h2 class="h2 mb-6">Contact Information</h2>
                        <div class="space-y-6">
                            <div>
                                <div class="contact-icon">
                                    <i class="ri-map-pin-line"></i>
                                </div>
                                <h3 class="h3 mb-2">Our Location</h3>
                                <p class="text-gray-400">7 Abou Rafea, Abu an Nawatir, <br> Sidi Gaber, Alexandria Governorate</p>
                            </div>
                            <div>
                                <div class="contact-icon">
                                    <i class="ri-mail-line"></i>
                                </div>
                                <h3 class="h3 mb-2">Email Us</h3>                                
                                <p class="text-gray-400"><a href="mailto:<EMAIL>" class="__cf_email__" data-cfemail="c3aaada5ac83b0a2abafa2b0acafb6b7aaacadb0eda0acae">Click here to Email Us</a></p>
                            </div>
                            <div>
                                <div class="contact-icon">
                                    <i class="ri-phone-line"></i>
                                </div>
                                <h3 class="h3 mb-2">Call Us</h3>
                                <p class="text-gray-400">+20 1553869950</p>
                            </div>
                            <div>
                                <div class="contact-icon">
                                    <i class="ri-time-line"></i>
                                </div>
                                <h3 class="h3 mb-2">Business Hours</h3>
                                <p class="text-gray-400">Saturday - Thursday: 8:00 AM - 6:00 PM<br>Friday: Closed</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="lg:col-span-2">
                    <p class="text-gray-400">
                        Please provide detailed information about your inquiry in the form below. A member of our specialized team will contact you via email or phone within 1-2 business days to discuss your needs and how we can help.
                    </p>
                    <span></span>
                    <div class="contact-form">

                        <form id="contact-form" onsubmit="return handleSubmit(event)">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div class="form-group">
                                    <label for="name" class="form-label">Full Name</label>
                                    <input type="text" id="name" name="name" class="form-input" required placeholder="John Doe">
                                </div>
                                <div class="form-group">
                                    <label for="email" class="form-label">Email Address</label>
                                    <input type="email" id="email" name="email" class="form-input" required placeholder="<EMAIL>">
                                </div>
                                <div class="form-group">
                                    <label for="phone" class="form-label">Phone Number</label>
                                    <input type="tel" id="phone" name="phone" class="form-input" placeholder="+20 ************">
                                </div>
                                <div class="form-group">
                                    <label for="company" class="form-label">Company Name</label>
                                    <input type="text" id="company" name="company" class="form-input" placeholder="Your Company">
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="service" class="form-label">Service Interest</label>
                                <select id="service" name="service" class="form-input" required>
                                    <option value="">Select a service</option>
                                    <option value="process-automation">General Inquiry</option>
                                    <option value="process-automation">Book an Automation Service Session</option>
                                    <option value="digital-transformation">Digital Transformation</option>
                                    <option value="technology-consulting">Technology Consulting</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Contact Preference</label>
                                <div class="flex gap-4 mt-2">
                                    <div class="flex items-center">
                                        <input type="radio" id="prefer-email" name="contact-preference" value="email" class="mr-2" checked>
                                        <label for="prefer-email">Email</label>
                                    </div>
                                    <div class="flex items-center">
                                        <input type="radio" id="prefer-phone" name="contact-preference" value="phone" class="mr-2">
                                        <label for="prefer-phone">Phone</label>
                                    </div>
                                </div>
                            </div>
                            <div id="preferred-hours" class="form-group hidden">
                                <label for="call-time" class="form-label">Preferred Call Time</label>
                                <select id="call-time" name="call-time" class="form-input">
                                    <option value="">Select preferred time</option>
                                    <option value="8-10">8:00 AM - 10:00 AM GMT+2</option>
                                    <option value="10-12">10:00 AM - 12:00 PM GMT+2</option>
                                    <option value="12-2">12:00 PM - 2:00 PM GMT+2</option>
                                    <option value="2-4">2:00 PM - 4:00 PM GMT+2</option>
                                    <option value="4-6">4:00 PM - 6:00 PM GMT+2</option>
                                </select>
                            </div>
                            <script data-cfasync="false" src="/cdn-cgi/scripts/5c5dd728/cloudflare-static/email-decode.min.js"></script><script>
                                const phonePreference = document.getElementById('prefer-phone');
                                const emailPreference = document.getElementById('prefer-email');
                                const preferredHours = document.getElementById('preferred-hours');
                                
                                phonePreference.addEventListener('change', function() {
                                    if (this.checked) {
                                        preferredHours.classList.remove('hidden');
                                    }
                                });
                                
                                emailPreference.addEventListener('change', function() {
                                    if (this.checked) {
                                        preferredHours.classList.add('hidden');
                                    }
                                });
                            </script>
                            <div class="form-group">
                                <label for="message" class="form-label">Message</label>
                                <textarea id="message" name="message" class="form-input form-textarea" required placeholder="Tell us about your project or inquiry..."></textarea>
                            </div>
                            <button type="submit" class="btn btn-primary w-full">Send Message</button>
                            <div id="success-message" class="success-message">
                                Thank you for your message! We'll get back to you soon.
                            </div>
                            <div id="error-message" class="error-message">
                                There was an error sending your message. Please try again.
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>

<!-- f -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <!-- Company Info Column -->
                <div class="footer-info">
                    <a href="../index.html" class="footer-logo">
                        <img src="../assets/images/Negative Horizontal Version.svg" alt="Sahla Smart Solutions" class="logo-img" id="foot-logo">
                    </a>
                    <p class="footer-description">Empowering businesses through innovative venture building and automation solutions in the MENA region.</p>
                    <div class="footer-social">
                        <a href="https://eg.linkedin.com/company/sahla-solutions" target="_blank" rel="noopener noreferrer"><i class="ri-linkedin-fill"></i></a>
                        <a href="https://x.com/sahlasolutions" target="_blank" rel="noopener noreferrer"><i class="ri-twitter-fill"></i></a>
                        <a href="https://www.facebook.com/SahlaSmartSolutions/" target="_blank" rel="noopener noreferrer"><i class="ri-facebook-fill"></i></a>
                        <a href="https://www.instagram.com/sahlasolutions/" target="_blank" rel="noopener noreferrer"><i class="ri-instagram-fill"></i></a>
                    </div>
                </div>

                <!-- Quick Links Column -->
                <div class="footer-links-column">
                    <h4>Quick Links</h4>
                    <ul class="footer-links-list">
                        <li><a href="../index.html">Home</a></li>
                        <li><a href="./about.html">About Us</a></li>
                        <li><a href="./ventures.html">Our Ventures</a></li>
                        <li><a href="./partners.html">Partners</a></li>
                        <li><a href="./careers.html">Careers</a></li>
                        <li><a href="./contact.html">Contact Us</a></li>
                    </ul>
                </div>

                <!-- Services Column -->
                <div class="footer-links-column">
                    <h4>Services</h4>
                    <ul class="footer-links-list">
                        <li><a href="./process-automation.html">Process Automation</a></li>
                        <li><a href="./technology-consulting.html">Technology Consulting</a></li>
                    </ul>
                </div>

                <!-- Newsletter Column -->
                <div class="footer-newsletter">
                    <h4>Newsletter</h4>

                    <form class="mb-4">
                        <div class="flex">
                            <!-- <input type="email" placeholder="Your email" class="w-full px-4 py-2 bg-gray-800 border-none text-white text-sm rounded-l"> -->
                            <a href="javascript:void(0)" class="ml-onclick-form" onclick="ml('show', 'gturk0', true)" style="
                                display: inline-block;
                                padding: 8px 16px;
                                background-color: var(--primary-color-1);
                                color: white;
                                font-size: 16px;
                                font-family: sans-serif;
                                border: none;
                                border-radius: 6px;
                                text-decoration: none;
                                cursor: pointer;
                                transition: background-color 0.3s ease;
                                " onmouseover="this.style.backgroundColor='var(--primary-color-2)'" onmouseout="this.style.backgroundColor= 'var(--primary-color-3)'">
                                Subscribe to Newsletter
                                </a>
                        </div>
                    </form>
                    <p>Subscribe to our newsletter to stay updated with our latest news and insights.</p>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-12 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <p class="text-gray-400 text-sm mb-4 md:mb-0">© 2025 Sahla Smart Solutions. All rights reserved.</p>
                    <div class="flex space-x-6">
                        <a href="../pages/privacy-policy.html" class="text-gray-400 hover:text-white text-sm">Privacy Policy</a>
                        <a href="../pages/terms-of-service.html" class="text-gray-400 hover:text-white text-sm">Terms of Service</a>
                        <a href="../pages/cookies-policy.html" class="text-gray-400 hover:text-white text-sm">Cookies Policy</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Theme switch functionality
        const themeSwitch = document.getElementById('theme-switch');
        const html = document.documentElement;
        const navLogo = document.getElementById('nav-logo');
        const footLogo = document.getElementById('foot-logo');

        // Function to set and save theme
        const setTheme = (theme) => {
            html.setAttribute('data-theme', theme);
            localStorage.setItem('theme', theme);
            themeSwitch.classList.toggle('dark', theme === 'dark');
            
            // Update logo based on theme
            if (theme === 'dark') {
                navLogo.src = '../assets/images/Dark Horizontal Version.svg';
                footLogo.src = '../assets/images/Dark Horizontal Version.svg';
            } else {
                navLogo.src = '../assets/images/Main Horizontal Version.svg';
                footLogo.src = '../assets/images/Main Horizontal Version.svg';
            }
        };

        // Set the correct theme class on icon container and logo after load
        document.addEventListener('DOMContentLoaded', () => {
            const currentTheme = html.getAttribute('data-theme');
            themeSwitch.classList.toggle('dark', currentTheme === 'dark');
            
            // Set initial logo based on theme
            if (currentTheme === 'dark') {
                navLogo.src = '../assets/images/Dark Horizontal Version.svg';
                footLogo.src = '../assets/images/Dark Horizontal Version.svg';
            } else {
                navLogo.src = '../assets/images/Main Horizontal Version.svg';
                footLogo.src = '../assets/images/Main Horizontal Version.svg';
            }
        });

        // Toggle theme on click
        themeSwitch.addEventListener('click', () => {
            const isCurrentlyDark = html.getAttribute('data-theme') === 'dark';
            const newTheme = isCurrentlyDark ? 'light' : 'dark';
            setTheme(newTheme);
        });

        // Watch for system theme changes
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
            if (!localStorage.getItem('theme')) {
                setTheme(e.matches ? 'dark' : 'light');
            }
        });

        // Mobile menu toggle
        const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
        const navLinks = document.querySelector('.nav-links');

        mobileMenuToggle.addEventListener('change', function() {
            if (this.checked) {
                navLinks.style.display = 'flex';
            } else {
                navLinks.style.display = '';
            }
        });
        
        // Form submission handler
        async function handleSubmit(event) {
            event.preventDefault();
            
            // Get form data
            const formData = new FormData(event.target);
            const data = Object.fromEntries(formData.entries());
            
            try {
                // Send email using EmailJS
                const response = await emailjs.send(
                    "service_4a54trt", // Replace with your EmailJS service ID
                    "template_4d74kx8", // Replace with your EmailJS template ID
                    {
                        to_email: "<EMAIL>",
                        from_name: data.name,
                        from_email: data.email,
                        phone: data.phone,
                        company: data.company,
                        service: data.service,
                        contact_preference: data['contact-preference'],
                        preferred_time: data['call-time'],
                        message: data.message
                    },
                    "m3l0WQJTHlQXDWEEG" // Replace with your EmailJS public key
                );

                if (response.status === 200) {
                    // Redirect to success page
                    window.location.href = './contact-success.html';
                } else {
                    throw new Error('Failed to send email');
                }
            } catch (error) {
                console.error('Error sending email:', error);
                document.getElementById('error-message').style.display = 'block';
                setTimeout(() => {
                    document.getElementById('error-message').style.display = 'none';
                }, 5000);
            }
            
            return false;
        }
    </script>

    <!-- Add EmailJS SDK -->
    <script src="https://cdn.jsdelivr.net/npm/@emailjs/browser@3/dist/email.min.js"></script>
    <script>
        // Initialize EmailJS
        emailjs.init("m3l0WQJTHlQXDWEEG"); // Replace with your EmailJS public key
    </script>
</body>
<script>'undefined'=== typeof _trfq || (window._trfq = []);'undefined'=== typeof _trfd && (window._trfd=[]),_trfd.push({'tccl.baseHost':'secureserver.net'},{'ap':'cpsh-oh'},{'server':'sxb1plzcpnl453516'},{'dcenter':'sxb1'},{'cp_id':'9153305'},{'cp_cache':''},{'cp_cl':'8'}) // Monitoring performance to make your website faster. If you want to opt-out, please contact web hosting support.</script><script src='https://img1.wsimg.com/traffic-assets/js/tccl.min.js'></script></html> 