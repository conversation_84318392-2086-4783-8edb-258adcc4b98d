<?php
header('Content-Type: application/json');
require_once __DIR__ . '/../BlogPost.php';

// Initialize BlogPost
BlogPost::init();

// Enable error reporting for debugging
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Set up error logging
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../../php_errors.log');

// Log request details
error_log('Request Method: ' . $_SERVER['REQUEST_METHOD']);
error_log('Request URI: ' . $_SERVER['REQUEST_URI']);

// Safely check for QUERY_STRING
if (isset($_SERVER['QUERY_STRING'])) {
    error_log('Query String: ' . $_SERVER['QUERY_STRING']);
}

if (isset($_GET['slug'])) {
    error_log('Slug from GET: ' . $_GET['slug']);
}

if (!empty($_SERVER['PATH_INFO'])) {
    error_log('Path Info: ' . $_SERVER['PATH_INFO']);
}

// Get request method and path
$method = $_SERVER['REQUEST_METHOD'];

// Get slug from query string or PATH_INFO
$slug = null;
if (isset($_GET['slug'])) {
    $slug = $_GET['slug'];
} elseif (!empty($_SERVER['PATH_INFO'])) {
    $slug = ltrim($_SERVER['PATH_INFO'], '/');
}

// Handle different HTTP methods
switch ($method) {
    case 'GET':
        if ($slug) {
            // Get single post
            $post = BlogPost::getPost($slug);
            if ($post) {
                echo json_encode($post);
            } else {
                http_response_code(404);
                echo json_encode(['error' => 'Post not found']);
            }
        } else {
            // No slug: return all posts
            $posts = BlogPost::getAllPosts();
            if (empty($posts)) {
                // Return an empty array instead of an error for the admin panel
                // This allows the admin panel to show a "no posts" message
                echo json_encode([]);
            } else {
                echo json_encode($posts);
            }
        }
        break;

    case 'POST':
        // Create new post
        error_log('POST request received to create a new post');

        // Get the raw input
        $rawInput = file_get_contents('php://input');
        error_log('Raw input: ' . $rawInput);

        // Decode JSON
        $data = json_decode($rawInput, true);
        if (!$data) {
            error_log('JSON decode error: ' . json_last_error_msg());
            http_response_code(400);
            echo json_encode(['error' => 'Invalid JSON data: ' . json_last_error_msg()]);
            break;
        }

        error_log('Decoded data: ' . print_r($data, true));

        // Validate required fields
        $required = ['title', 'author', 'content', 'category'];
        foreach ($required as $field) {
            if (empty($data[$field])) {
                error_log('Missing required field: ' . $field);
                http_response_code(400);
                echo json_encode(['error' => "Missing required field: $field"]);
                exit;
            }
        }

        // Set default release date if not provided
        if (empty($data['release_date'])) {
            $data['release_date'] = date('Y-m-d');
            error_log('Using default release date: ' . $data['release_date']);
        }

        // Attempt to create the post
        error_log('Attempting to create post with title: ' . $data['title']);
        if (BlogPost::createPost($data)) {
            error_log('Post created successfully');
            http_response_code(201);
            echo json_encode(['message' => 'Post created successfully']);
        } else {
            error_log('Failed to create post');
            http_response_code(500);
            echo json_encode(['error' => 'Failed to create post']);
        }
        break;

    case 'PUT':
        // Update existing post
        error_log('PUT request received to update post');

        if (!$slug) {
            error_log('No slug provided for PUT request');
            http_response_code(400);
            echo json_encode(['error' => 'Post slug required']);
            break;
        }

        error_log('Updating post with slug: ' . $slug);

        // Get the raw input
        $rawInput = file_get_contents('php://input');
        error_log('Raw input: ' . $rawInput);

        // Decode JSON
        $data = json_decode($rawInput, true);
        if (!$data) {
            error_log('JSON decode error: ' . json_last_error_msg());
            http_response_code(400);
            echo json_encode(['error' => 'Invalid JSON data: ' . json_last_error_msg()]);
            break;
        }

        error_log('Decoded data: ' . print_r($data, true));

        // Check if post exists
        $existingPost = BlogPost::getPost($slug);
        if (!$existingPost) {
            error_log('Post not found with slug: ' . $slug);
            http_response_code(404);
            echo json_encode(['error' => 'Post not found']);
            break;
        }

        // Attempt to update the post
        error_log('Attempting to update post with slug: ' . $slug);
        if (BlogPost::updatePost($slug, $data)) {
            error_log('Post updated successfully');
            echo json_encode(['message' => 'Post updated successfully']);
        } else {
            error_log('Failed to update post');
            http_response_code(500);
            echo json_encode(['error' => 'Update failed']);
        }
        break;

    case 'DELETE':
        // Delete post
        if (!$slug) {
            http_response_code(400);
            echo json_encode(['error' => 'Post slug required']);
            break;
        }

        if (BlogPost::deletePost($slug)) {
            echo json_encode(['message' => 'Post deleted successfully']);
        } else {
            http_response_code(404);
            echo json_encode(['error' => 'Post not found or delete failed']);
        }
        break;

    default:
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
        break;
}