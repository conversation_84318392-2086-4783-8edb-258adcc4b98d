<?php
header('Content-Type: application/json');
require_once __DIR__ . '/../BlogPost.php';

// Initialize BlogPost
BlogPost::init();

// Enable error reporting for debugging
ini_set('display_errors', 1);
error_reporting(E_ALL);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../../php_errors.log');

/**
 * Download image from URL and save to assets/images/blog directory
 * @param string $imageUrl The URL of the image to download
 * @return string|false The filename of the saved image or false on failure
 */
function downloadAndSaveImage($imageUrl)
{
    try {
        // Validate URL
        if (!filter_var($imageUrl, FILTER_VALIDATE_URL)) {
            error_log('Invalid image URL: ' . $imageUrl);
            return false;
        }

        // Check if URL starts with http or https
        if (!preg_match('/^https?:\/\//', $imageUrl)) {
            error_log('Image URL must start with http:// or https://: ' . $imageUrl);
            return false;
        }

        // Get image content using cURL
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $imageUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_MAXREDIRS, 5);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; SahlaWebsite/1.0)');
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // For HTTPS URLs
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

        $imageContent = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        curl_close($ch);

        if ($imageContent === false || !empty($curlError)) {
            error_log('Failed to download image from URL: ' . $imageUrl . ' - cURL error: ' . $curlError);
            return false;
        }

        if ($httpCode !== 200) {
            error_log('Failed to download image from URL: ' . $imageUrl . ' - HTTP code: ' . $httpCode);
            return false;
        }

        // Get image info to validate format and get extension
        $imageInfo = getimagesizefromstring($imageContent);
        if ($imageInfo === false) {
            error_log('Invalid image format from URL: ' . $imageUrl);
            return false;
        }

        // Map MIME types to extensions
        $mimeToExt = [
            'image/jpeg' => 'jpg',
            'image/png' => 'png',
            'image/gif' => 'gif',
            'image/webp' => 'webp'
        ];

        $mimeType = $imageInfo['mime'];
        if (!isset($mimeToExt[$mimeType])) {
            error_log('Unsupported image format: ' . $mimeType . ' from URL: ' . $imageUrl);
            return false;
        }

        $extension = $mimeToExt[$mimeType];

        // Generate unique filename
        $timestamp = time();
        $randomString = bin2hex(random_bytes(8));
        $filename = "cover-{$timestamp}-{$randomString}.{$extension}";

        // Ensure the blog images directory exists
        $blogImagesDir = __DIR__ . '/../../assets/images/blog/';
        if (!file_exists($blogImagesDir)) {
            if (!mkdir($blogImagesDir, 0777, true)) {
                error_log('Failed to create blog images directory: ' . $blogImagesDir);
                return false;
            }
        }

        // Save the image
        $filepath = $blogImagesDir . $filename;
        if (file_put_contents($filepath, $imageContent) === false) {
            error_log('Failed to save image to: ' . $filepath);
            return false;
        }

        error_log('Successfully downloaded and saved image: ' . $filename . ' from URL: ' . $imageUrl);
        return $filename;

    } catch (Exception $e) {
        error_log('Error downloading image from URL ' . $imageUrl . ': ' . $e->getMessage());
        return false;
    }
}

// Log request details
error_log('Request Method: ' . $_SERVER['REQUEST_METHOD']);
error_log('Request URI: ' . $_SERVER['REQUEST_URI']);

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

try {
    // Check if we have a file upload
    if (isset($_FILES['markdown_file']) && $_FILES['markdown_file']['error'] === UPLOAD_ERR_OK) {
        // File upload method
        error_log('Processing file upload');

        // Get file content
        $markdownContent = file_get_contents($_FILES['markdown_file']['tmp_name']);
        if ($markdownContent === false) {
            error_log('Failed to read uploaded file');
            http_response_code(500);
            echo json_encode(['error' => 'Failed to read uploaded file']);
            exit;
        }

        // Get form data
        $title = $_POST['title'] ?? '';
        $author = $_POST['author'] ?? '';
        $category = $_POST['category'] ?? '';
        $releaseDate = $_POST['release_date'] ?? date('Y-m-d');
        $tags = isset($_POST['tags']) ? explode(',', $_POST['tags']) : [];
        $summary = $_POST['summary'] ?? '';
        $coverImage = $_POST['cover_image'] ?? 'blog-placeholder.webp';
        $readingTime = $_POST['reading_time'] ?? 5;
        $draft = isset($_POST['draft']) ? filter_var($_POST['draft'], FILTER_VALIDATE_BOOLEAN) : false;

        // Clean up tags array
        $tags = array_map('trim', $tags);
        $tags = array_filter($tags, function ($tag) {
            return !empty($tag);
        });

    } else {
        // JSON method
        error_log('Processing JSON data');

        // Get the raw input
        $rawInput = file_get_contents('php://input');
        error_log('Raw input: ' . $rawInput);

        // Decode JSON
        $data = json_decode($rawInput, true);
        if (!$data) {
            error_log('JSON decode error: ' . json_last_error_msg());
            http_response_code(400);
            echo json_encode(['error' => 'Invalid JSON data: ' . json_last_error_msg()]);
            exit;
        }

        // Extract data from JSON
        $title = $data['title'] ?? '';
        $author = $data['author'] ?? '';
        $category = $data['category'] ?? '';
        $markdownContent = $data['markdown_content'] ?? '';
        $releaseDate = $data['release_date'] ?? date('Y-m-d');
        $tags = $data['tags'] ?? [];
        $summary = $data['summary'] ?? '';
        $coverImage = $data['cover_image'] ?? 'blog-placeholder.webp';
        $readingTime = $data['reading_time'] ?? 5;
        $draft = $data['draft'] ?? false;
    }

    // Check for required fields
    if (empty($title) || empty($author) || empty($category) || empty($markdownContent)) {
        error_log('Missing required fields');
        http_response_code(400);
        echo json_encode(['error' => 'Missing required fields: title, author, category, and markdown content are required']);
        exit;
    }

    // Process cover image if it's a URL
    if (!empty($coverImage) && filter_var($coverImage, FILTER_VALIDATE_URL)) {
        error_log('Cover image is a URL, attempting to download: ' . $coverImage);
        $downloadedFilename = downloadAndSaveImage($coverImage);

        if ($downloadedFilename !== false) {
            $coverImage = $downloadedFilename;
            error_log('Successfully processed cover image URL, using filename: ' . $coverImage);
        } else {
            error_log('Failed to download cover image from URL: ' . $coverImage . ', using default placeholder');
            $coverImage = 'blog-placeholder.webp';
        }
    } else {
        error_log('Cover image is not a URL or is empty, using as-is: ' . $coverImage);
    }

    // Prepare the post data
    $postData = [
        'title' => $title,
        'author' => $author,
        'release_date' => $releaseDate,
        'category' => $category,
        'tags' => $tags,
        'summary' => $summary,
        'cover_image' => $coverImage,
        'reading_time' => (int) $readingTime,
        'draft' => $draft,
        'content' => $markdownContent
    ];

    // Create the post
    if (BlogPost::createPost($postData)) {
        http_response_code(201);
        echo json_encode(['message' => 'Markdown file uploaded and processed successfully']);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to create post']);
    }
} catch (Exception $e) {
    error_log('Error processing markdown upload: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Server error: ' . $e->getMessage()]);
}
