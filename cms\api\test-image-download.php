<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Enable error reporting for debugging
ini_set('display_errors', 1);
error_reporting(E_ALL);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../../php_errors.log');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed. Use POST.']);
    exit;
}

/**
 * Download image from URL and save to assets/images/blog directory
 * @param string $imageUrl The URL of the image to download
 * @return array Result with success status, filename, and message
 */
function downloadAndSaveImage($imageUrl)
{
    try {
        // Validate URL
        if (!filter_var($imageUrl, FILTER_VALIDATE_URL)) {
            return [
                'success' => false,
                'error' => 'Invalid image URL: ' . $imageUrl,
                'filename' => null
            ];
        }

        // Check if URL starts with http or https
        if (!preg_match('/^https?:\/\//', $imageUrl)) {
            return [
                'success' => false,
                'error' => 'Image URL must start with http:// or https://: ' . $imageUrl,
                'filename' => null
            ];
        }

        // Get image content using cURL
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $imageUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_MAXREDIRS, 5);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; SahlaWebsite/1.0)');
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // For HTTPS URLs
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

        $imageContent = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        curl_close($ch);

        if ($imageContent === false || !empty($curlError)) {
            return [
                'success' => false,
                'error' => 'Failed to download image from URL: ' . $imageUrl . ' - cURL error: ' . $curlError,
                'filename' => null
            ];
        }

        if ($httpCode !== 200) {
            return [
                'success' => false,
                'error' => 'Failed to download image from URL: ' . $imageUrl . ' - HTTP code: ' . $httpCode,
                'filename' => null
            ];
        }

        // Get image info to validate format and get extension
        $imageInfo = getimagesizefromstring($imageContent);
        if ($imageInfo === false) {
            return [
                'success' => false,
                'error' => 'Invalid image format from URL: ' . $imageUrl,
                'filename' => null
            ];
        }

        // Map MIME types to extensions
        $mimeToExt = [
            'image/jpeg' => 'jpg',
            'image/png' => 'png',
            'image/gif' => 'gif',
            'image/webp' => 'webp'
        ];

        $mimeType = $imageInfo['mime'];
        if (!isset($mimeToExt[$mimeType])) {
            return [
                'success' => false,
                'error' => 'Unsupported image format: ' . $mimeType . ' from URL: ' . $imageUrl,
                'filename' => null
            ];
        }

        $extension = $mimeToExt[$mimeType];

        // Generate unique filename
        $timestamp = time();
        $randomString = bin2hex(random_bytes(8));
        $filename = "cover-{$timestamp}-{$randomString}.{$extension}";

        // Ensure the blog images directory exists
        $blogImagesDir = __DIR__ . '/../../assets/images/blog/';
        if (!file_exists($blogImagesDir)) {
            if (!mkdir($blogImagesDir, 0777, true)) {
                return [
                    'success' => false,
                    'error' => 'Failed to create blog images directory: ' . $blogImagesDir,
                    'filename' => null
                ];
            }
        }

        // Save the image
        $filepath = $blogImagesDir . $filename;
        if (file_put_contents($filepath, $imageContent) === false) {
            return [
                'success' => false,
                'error' => 'Failed to save image to: ' . $filepath,
                'filename' => null
            ];
        }

        return [
            'success' => true,
            'message' => 'Successfully downloaded and saved image',
            'filename' => $filename,
            'filepath' => $filepath,
            'size' => strlen($imageContent),
            'dimensions' => $imageInfo[0] . 'x' . $imageInfo[1],
            'mime_type' => $mimeType
        ];

    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => 'Error downloading image from URL ' . $imageUrl . ': ' . $e->getMessage(),
            'filename' => null
        ];
    }
}

try {
    // Get the raw input
    $rawInput = file_get_contents('php://input');
    error_log('Test API - Raw input: ' . $rawInput);

    // Decode JSON
    $data = json_decode($rawInput, true);
    if (!$data) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => 'Invalid JSON data: ' . json_last_error_msg(),
            'raw_input' => $rawInput
        ]);
        exit;
    }

    // Check if image_url is provided
    if (empty($data['image_url'])) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => 'Missing required field: image_url',
            'example' => [
                'image_url' => 'https://images.pexels.com/photos/8386440/pexels-photo-8386440.jpeg'
            ]
        ]);
        exit;
    }

    $imageUrl = $data['image_url'];
    error_log('Test API - Processing image URL: ' . $imageUrl);

    // Download and save the image
    $result = downloadAndSaveImage($imageUrl);

    if ($result['success']) {
        http_response_code(200);
        echo json_encode([
            'success' => true,
            'message' => 'Image downloaded and saved successfully!',
            'data' => $result,
            'blog_images_directory' => '/assets/images/blog/',
            'full_image_path' => '/assets/images/blog/' . $result['filename']
        ]);
    } else {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => $result['error'],
            'image_url' => $imageUrl
        ]);
    }

} catch (Exception $e) {
    error_log('Test API - Error: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Server error: ' . $e->getMessage()
    ]);
}
?>