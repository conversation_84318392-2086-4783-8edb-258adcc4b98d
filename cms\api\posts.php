<?php
header('Content-Type: application/json');
require_once __DIR__ . '/../BlogPost.php';

// Initialize BlogPost
BlogPost::init();

// Enable error reporting for debugging
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Set up error logging
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../../php_errors.log');

/**
 * Download image from URL and save to assets/images/blog directory
 * @param string $imageUrl The URL of the image to download
 * @return string|false The filename of the saved image or false on failure
 */
function downloadAndSaveImage($imageUrl)
{
    try {
        // Validate URL
        if (!filter_var($imageUrl, FILTER_VALIDATE_URL)) {
            error_log('Invalid image URL: ' . $imageUrl);
            return false;
        }

        // Check if URL starts with http or https
        if (!preg_match('/^https?:\/\//', $imageUrl)) {
            error_log('Image URL must start with http:// or https://: ' . $imageUrl);
            return false;
        }

        // Get image content
        $context = stream_context_create([
            'http' => [
                'timeout' => 30,
                'user_agent' => 'Mozilla/5.0 (compatible; SahlaWebsite/1.0)'
            ]
        ]);

        $imageContent = file_get_contents($imageUrl, false, $context);
        if ($imageContent === false) {
            error_log('Failed to download image from URL: ' . $imageUrl);
            return false;
        }

        // Get image info to validate format and get extension
        $imageInfo = getimagesizefromstring($imageContent);
        if ($imageInfo === false) {
            error_log('Invalid image format from URL: ' . $imageUrl);
            return false;
        }

        // Map MIME types to extensions
        $mimeToExt = [
            'image/jpeg' => 'jpg',
            'image/png' => 'png',
            'image/gif' => 'gif',
            'image/webp' => 'webp'
        ];

        $mimeType = $imageInfo['mime'];
        if (!isset($mimeToExt[$mimeType])) {
            error_log('Unsupported image format: ' . $mimeType . ' from URL: ' . $imageUrl);
            return false;
        }

        $extension = $mimeToExt[$mimeType];

        // Generate unique filename
        $timestamp = time();
        $randomString = bin2hex(random_bytes(8));
        $filename = "cover-{$timestamp}-{$randomString}.{$extension}";

        // Ensure the blog images directory exists
        $blogImagesDir = __DIR__ . '/../../assets/images/blog/';
        if (!file_exists($blogImagesDir)) {
            if (!mkdir($blogImagesDir, 0777, true)) {
                error_log('Failed to create blog images directory: ' . $blogImagesDir);
                return false;
            }
        }

        // Save the image
        $filepath = $blogImagesDir . $filename;
        if (file_put_contents($filepath, $imageContent) === false) {
            error_log('Failed to save image to: ' . $filepath);
            return false;
        }

        error_log('Successfully downloaded and saved image: ' . $filename . ' from URL: ' . $imageUrl);
        return $filename;

    } catch (Exception $e) {
        error_log('Error downloading image from URL ' . $imageUrl . ': ' . $e->getMessage());
        return false;
    }
}

// Log request details
error_log('Request Method: ' . $_SERVER['REQUEST_METHOD']);
error_log('Request URI: ' . $_SERVER['REQUEST_URI']);

// Safely check for QUERY_STRING
if (isset($_SERVER['QUERY_STRING'])) {
    error_log('Query String: ' . $_SERVER['QUERY_STRING']);
}

if (isset($_GET['slug'])) {
    error_log('Slug from GET: ' . $_GET['slug']);
}

if (!empty($_SERVER['PATH_INFO'])) {
    error_log('Path Info: ' . $_SERVER['PATH_INFO']);
}

// Get request method and path
$method = $_SERVER['REQUEST_METHOD'];

// Get slug from query string or PATH_INFO
$slug = null;
if (isset($_GET['slug'])) {
    $slug = $_GET['slug'];
} elseif (!empty($_SERVER['PATH_INFO'])) {
    $slug = ltrim($_SERVER['PATH_INFO'], '/');
}

// Handle different HTTP methods
switch ($method) {
    case 'GET':
        if ($slug) {
            // Get single post
            $post = BlogPost::getPost($slug);
            if ($post) {
                echo json_encode($post);
            } else {
                http_response_code(404);
                echo json_encode(['error' => 'Post not found']);
            }
        } else {
            // No slug: return all posts
            $posts = BlogPost::getAllPosts();
            if (empty($posts)) {
                // Return an empty array instead of an error for the admin panel
                // This allows the admin panel to show a "no posts" message
                echo json_encode([]);
            } else {
                echo json_encode($posts);
            }
        }
        break;

    case 'POST':
        // Create new post
        error_log('POST request received to create a new post');

        // Get the raw input
        $rawInput = file_get_contents('php://input');
        error_log('Raw input: ' . $rawInput);

        // Decode JSON
        $data = json_decode($rawInput, true);
        if (!$data) {
            error_log('JSON decode error: ' . json_last_error_msg());
            http_response_code(400);
            echo json_encode(['error' => 'Invalid JSON data: ' . json_last_error_msg()]);
            break;
        }

        error_log('Decoded data: ' . print_r($data, true));

        // Validate required fields
        $required = ['title', 'author', 'content', 'category'];
        foreach ($required as $field) {
            if (empty($data[$field])) {
                error_log('Missing required field: ' . $field);
                http_response_code(400);
                echo json_encode(['error' => "Missing required field: $field"]);
                exit;
            }
        }

        // Set default release date if not provided
        if (empty($data['release_date'])) {
            $data['release_date'] = date('Y-m-d');
            error_log('Using default release date: ' . $data['release_date']);
        }

        // Process cover image if it's a URL
        if (!empty($data['cover_image']) && filter_var($data['cover_image'], FILTER_VALIDATE_URL)) {
            error_log('Cover image is a URL, attempting to download: ' . $data['cover_image']);
            $downloadedFilename = downloadAndSaveImage($data['cover_image']);

            if ($downloadedFilename !== false) {
                $data['cover_image'] = $downloadedFilename;
                error_log('Successfully processed cover image URL, using filename: ' . $data['cover_image']);
            } else {
                error_log('Failed to download cover image from URL: ' . $data['cover_image'] . ', using default placeholder');
                $data['cover_image'] = 'blog-placeholder.webp';
            }
        } else {
            error_log('Cover image is not a URL or is empty, using as-is: ' . ($data['cover_image'] ?? 'not set'));
        }

        // Attempt to create the post
        error_log('Attempting to create post with title: ' . $data['title']);
        if (BlogPost::createPost($data)) {
            error_log('Post created successfully');
            http_response_code(201);
            echo json_encode(['message' => 'Post created successfully']);
        } else {
            error_log('Failed to create post');
            http_response_code(500);
            echo json_encode(['error' => 'Failed to create post']);
        }
        break;

    case 'PUT':
        // Update existing post
        error_log('PUT request received to update post');

        if (!$slug) {
            error_log('No slug provided for PUT request');
            http_response_code(400);
            echo json_encode(['error' => 'Post slug required']);
            break;
        }

        error_log('Updating post with slug: ' . $slug);

        // Get the raw input
        $rawInput = file_get_contents('php://input');
        error_log('Raw input: ' . $rawInput);

        // Decode JSON
        $data = json_decode($rawInput, true);
        if (!$data) {
            error_log('JSON decode error: ' . json_last_error_msg());
            http_response_code(400);
            echo json_encode(['error' => 'Invalid JSON data: ' . json_last_error_msg()]);
            break;
        }

        error_log('Decoded data: ' . print_r($data, true));

        // Check if post exists
        $existingPost = BlogPost::getPost($slug);
        if (!$existingPost) {
            error_log('Post not found with slug: ' . $slug);
            http_response_code(404);
            echo json_encode(['error' => 'Post not found']);
            break;
        }

        // Process cover image if it's a URL
        if (!empty($data['cover_image']) && filter_var($data['cover_image'], FILTER_VALIDATE_URL)) {
            error_log('Cover image is a URL, attempting to download: ' . $data['cover_image']);
            $downloadedFilename = downloadAndSaveImage($data['cover_image']);

            if ($downloadedFilename !== false) {
                $data['cover_image'] = $downloadedFilename;
                error_log('Successfully processed cover image URL, using filename: ' . $data['cover_image']);
            } else {
                error_log('Failed to download cover image from URL: ' . $data['cover_image'] . ', keeping original');
                // For updates, we don't change the cover_image if download fails
                unset($data['cover_image']);
            }
        } else {
            error_log('Cover image is not a URL or is empty, using as-is: ' . ($data['cover_image'] ?? 'not set'));
        }

        // Attempt to update the post
        error_log('Attempting to update post with slug: ' . $slug);
        if (BlogPost::updatePost($slug, $data)) {
            error_log('Post updated successfully');
            echo json_encode(['message' => 'Post updated successfully']);
        } else {
            error_log('Failed to update post');
            http_response_code(500);
            echo json_encode(['error' => 'Update failed']);
        }
        break;

    case 'DELETE':
        // Delete post
        if (!$slug) {
            http_response_code(400);
            echo json_encode(['error' => 'Post slug required']);
            break;
        }

        if (BlogPost::deletePost($slug)) {
            echo json_encode(['message' => 'Post deleted successfully']);
        } else {
            http_response_code(404);
            echo json_encode(['error' => 'Post not found or delete failed']);
        }
        break;

    default:
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
        break;
}