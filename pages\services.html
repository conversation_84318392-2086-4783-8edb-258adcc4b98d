<!DOCTYPE html>
<html lang="en">

<head>     <link rel="icon" type="image/x-icon" href="../assets/images/favicon.ico?v=2">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Services - Sahla Smart Solutions</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#0056b3',
                        secondary: '#4dabf7'
                    },
                    borderRadius: {
                        'none': '0px',
                        'sm': '4px',
                        DEFAULT: '8px',
                        'md': '12px',
                        'lg': '16px',
                        'xl': '20px',
                        '2xl': '24px',
                        '3xl': '32px',
                        'full': '9999px',
                        'button': '8px'
                    }
                }
            }
        }
    </script>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/navigation.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;400;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@4.5.0/fonts/remixicon.css" rel="stylesheet">
    <!-- MailerLite Universal -->
    <script>
        (function(w, d, e, u, f, l, n) {
            w[f] = w[f] || function() {
                    (w[f].q = w[f].q || [])
                    .push(arguments);
                }, l = d.createElement(e), l.async = 1, l.src = u,
                n = d.getElementsByTagName(e)[0], n.parentNode.insertBefore(l, n);
        })
        (window, document, 'script', 'https://assets.mailerlite.com/js/universal.js', 'ml');
        ml('account', '1446707');
    </script>
    <!-- End MailerLite Universal -->
    <style>
         :where([class^="ri-"])::before {
            content: "\f3c2";
        }
        
        .hero-section1 {
            padding: 13%;
            background-size: cover;
            background-position: center;
            background-color: var(--background-color);
            /* padding: 8rem 0; */
        }
        
        .highlight {
            color: var(--primary-color-1);
            /* blue text */
            position: relative;
            display: inline-block;
        }
        
        .highlight::after {
            content: '';
            position: absolute;
            left: 0;
            bottom: 0.2em;
            width: 100%;
            height: 0.6em;
            background-repeat: no-repeat;
            background-size: 100% 100%;
            pointer-events: none;
        }
        
        .service-card {
            border-radius: 1rem;
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .service-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }
        
        .stats-table {
            width: 100%;
            border-collapse: collapse;
            margin: 2rem 0;
        }
        
        .stats-table th,
        .stats-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }
        
        .stats-table th {
            background-color: var(--background-color-light);
            font-weight: 400;
        }
        
        .success-card {
            border-radius: 1rem;
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            background-color: var(--background-color);
        }
        
        .success-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }
        
        .faq-item {
            border-bottom: 1px solid var(--border-color);
            margin-bottom: 1rem;
        }
        
        .faq-question {
            font-weight: 400;
            cursor: pointer;
            padding: 1rem 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .faq-answer {
            padding-bottom: 1rem;
            display: none;
        }
        
        .bg-primary-100 {
            background-color: rgba(0, 86, 179, 0.1);
        }
        /* Service selector styles */
        
        .service-selector {
            padding: 1rem;
            border-radius: 0.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
            opacity: 0.8;
        }
        
        .service-selector:hover {
            background-color: rgba(255, 255, 255, 0.1);
            opacity: 1;
        }
        
        .service-selector.active {
            background-color: rgba(255, 255, 255, 0.2);
            position: relative;
            opacity: 1;
        }
        
        .service-selector.active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background-color: white;
            border-radius: 4px;
        }
        
        .service-image-container {
            min-height: 300px;
            position: relative;
        }
        
        .service-image {
            transition: opacity 0.5s ease;
        }
        /* Add browser window styling */
        
        .browser-window {
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            transform: perspective(1000px) rotateX(2deg);
            transition: transform 0.3s ease;
        }
        
        .browser-window:hover {
            transform: perspective(1000px) rotateX(0);
        }
        /* Custom background for interactive demo section */
        
        .demo-section {
            background-image: linear-gradient(to right, rgba(72, 7, 159, 0.6), rgba(4, 96, 191, 0.6)), url('https://salient.tailwindui.com/_next/static/media/background-features.5f7a9ac9.jpg');
            background-size: cover;
            background-position: center;
        }
        /* Make the demo image fill the right side */
        
        .demo-image-container {
            position: relative;
            width: 100%;
            height: 100%;
        }
        
        @media (min-width: 1024px) {
            .demo-right-col {
                position: absolute;
                right: -30%;
                top: 20%;
                width: 70%;
                height: 100%;
            }
            .demo-browser-container {
                position: absolute;
                top: 50%;
                transform: translateY(-50%);
                width: calc(100% + 8rem);
                max-width: none;
                right: 0;
            }
        }
        /* Custom background for FAQ section */
        
        .faq-section {
            background-image: linear-gradient(to right, rgba(134, 140, 255, 0.1), rgba(67, 24, 255, 0.1));
            background-size: cover;
            background-position: center;
        }
        
        .faq-columns {
            display: grid;
            grid-template-columns: 1fr;
            gap: 1rem 4rem;
        }
        
        @media (min-width: 768px) {
            .faq-columns {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        
        .faq-item {
            padding: 1.5rem 0;
            background-color: transparent;
            box-shadow: none;
        }
        
        .faq-question {
            padding: 0;
            margin-bottom: 0.75rem;
            font-size: 1.125rem;
            font-weight: 400;
            color: #111827;
        }
        
        .faq-answer {
            display: block;
            color: #6b7280;
            font-size: 0.9375rem;
            line-height: 1.5;
            padding: 0;
        }
    </style>
</head>

<body class="bg-white">
    <!-- Header & Navigation -->
    <header class="header">
        <div class="container">
            <nav class="nav">
                <a href="../index.html" class="nav-logo">
                    <img src="../assets/images/Main Horizontal Version.svg" alt="Sahla Logo" class="logo-img" id="nav-logo">
                </a>
                <input type="checkbox" id="mobile-menu-toggle" class="mobile-menu-toggle">
                <label for="mobile-menu-toggle" class="mobile-menu-button">
                    <span></span>
                    <span></span>
                    <span></span>
                </label>
                <div class="nav-links">
                    <div class="dropdown">
                        <a href="#services" class="nav-link">Services</a>
                        <div class="dropdown-content">
                            
                            <a href="process-automation.html" class="dropdown-link">Process Automation</a>
                            <a href="technology-consulting.html" class="dropdown-link">Technology Consulting</a>
                        </div>
                    </div>
                    <a href="ventures.html" class="nav-link">Our Ventures</a>
                    <a href="partners.html" class="nav-link">Partners</a>
                    <a href="careers.html" class="nav-link">Careers</a>
                    <a href="blog.html" class="nav-link">Blog</a>
                    <a href="about.html" class="nav-link">About us</a>
                    <div class="px-4">
                        <div class="h-6 border-l-2 border-gray-300"></div>
                    </div>
                    <a href="contact.html" class="bg-light px-6 py-2 !rounded-button hover:bg-opacity-90 transition-all">Contact Sales</a>
                    <div class="hidden md:flex items-center gap-4">
                        <div class="theme-switch" id="theme-switch">
                            <i class="ri-moon-line moon-icon"></i>
                            <i class="ri-sun-line sun-icon"></i>
                            <script>
                                (() => {
                                    const savedTheme = localStorage.getItem('theme');
                                    const preferred = savedTheme || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                                    document.documentElement.setAttribute('data-theme', preferred);
                                })();
                            </script>
                        </div>
                    </div>
                </div>
            </nav>
        </div>
    </header>

    <main>
        <!-- Hero Section -->
        <section class="hero-section1 text-center">
            <div class="container mx-auto px-4">
                <div class="max-w-6xl mx-auto">
                    <h1 class="text-7xl sm:text-7xl font-bold mb-6">
                        Scale
                        <span class="relative inline-block">
    <!-- Scribble SVG -->
    <svg aria-hidden="true" viewBox="0 0 418 42"
      class="absolute top-2/3 left-0 w-full h-[0.38em] fill-blue-300/70 z-0 pointer-events-none"
      preserveAspectRatio="none">
      <path
        d="M203.371.916c-26.013-2.078-76.686 1.963-124.73 9.946L67.3 12.749C35.421
        18.062 18.2 21.766 6.004 25.934 1.244 27.561.828 27.778.874 28.61c.07 1.214.828
        1.121 9.595-1.176 9.072-2.377 17.15-3.92 39.246-7.496C123.565 7.986 157.869
        4.492 195.942 5.046c7.461.108 19.25 1.696 19.17 2.582-.107 1.183-7.874
        4.31-25.75 10.366-21.992 7.45-35.43 12.534-36.701 13.884-2.173
        2.308-.202 4.407 4.442 4.734 2.654.187 3.263.157 15.593-.78
        35.401-2.686 57.944-3.488 88.365-3.143 46.327.526 75.721
        2.23 130.788 7.584 19.787 1.924 20.814 1.98 24.557
        1.332l.066-.011c1.201-.203 1.53-1.825.399-2.335-2.911-1.31-4.893-1.604-22.048-3.261-57.509-5.556-87.871-7.36-132.059-7.842-23.239-.254-33.617-.116-50.627.674-11.629.54-42.371
        2.494-46.696 2.967-2.359.259 8.133-3.625 26.504-9.81
        23.239-7.825 27.934-10.149 28.304-14.005.417-4.348-3.529-6-16.878-7.066Z" />
    </svg>
    <!-- Highlighted text -->
    <span class="relative z-10 text-blue-400">Smarter</span>
                        </span>
                        with Intelligent Process <span class="text-blue-400">Automation</span>
                    </h1>
                    <p class="text-xl opacity-90 mb-10 font-light ">From AI agents to workflow integrations — free your team from repetitive tasks and refocus them on what truly matters.</p>
                    <div class="flex flex-col sm:flex-row gap-4 justify-center ">
                        <a href="contact.html " class="button button-primary ">Get a Free Automation Consultation</a>
                        <a href="#use-cases " class="button button-secondary ">Explore Use Cases</a>
                    </div>
                </div>
            </div>

            <div class="container mx-auto px-4 text-center pt-20">
                <h3 class="h3 text-gray-400 text-base font-medium mb-10">
                    Trusted by fast-moving teams who get things done
                </h3>

                <div class="flex flex-wrap justify-center items-center gap-10">
                    <!-- Logo 1 -->
                    <img src="../assets/images/Transistor.svg" alt="Transistor" class="h-8">

                    <!-- Logo 2 -->
                    <img src="../assets/images/Tuple.svg" alt="Tuple" class="h-8">

                    <!-- Logo 3 -->
                    <img src="../assets/images/StaticKit.svg" alt="StaticKit" class="h-8">

                    <!-- Logo 4 -->
                    <img src="../assets/images/Mirage.svg" alt="Mirage" class="h-8">

                    <!-- Logo 5 -->
                    <img src="../assets/images/Laravel.svg" alt="Laravel" class="h-8">

                    <!-- Logo 6 -->
                    <img src="../assets/images/Statamic.svg" alt="Statamic" class="h-8">
                </div>
            </div>
        </section>

        <!-- Interactive Demo Section -->
        <section class="py-24 demo-section text-white relative overflow-hidden ">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative ">
                <div class="mx-auto max-w-7xl lg:grid lg:grid-cols-2 lg:gap-8 relative ">
                    <!-- Left column with text and selectors -->
                    <div class="z-10 relative ">
                        <h2 class="text-4xl sm:text-5xl font-bold mb-6 ">Everything you need to automate your workflow.</h2>
                        <p class="text-xl mb-12 opacity-90 text-white ">Free your team from repetitive tasks if you want them to focus on what truly creates value.</p>

                        <!-- Service Selector -->
                        <div class="space-y-6 ">
                            <div class="service-selector " data-service="chatbots ">
                                <h3 class="text-xl font-light flex items-center ">
                                    <span class="text-2xl mr-3">🤖</span> AI-Powered Chatbots
                                </h3>
                                <p class="pl-9 mt-2 text-white ">Automate customer service and lead qualification with intelligent conversational agents.</p>
                            </div>

                            <div class="service-selector " data-service="scheduling ">
                                <h3 class="text-xl font-light flex items-center ">
                                    <span class="text-2xl mr-3 ">📅</span> Smart Scheduling
                                </h3>
                                <p class="pl-9 mt-2 text-white ">Automate meeting bookings and reminders across your organization.</p>
                            </div>

                            <div class="service-selector " data-service="documents ">
                                <h3 class="text-xl font-light flex items-center ">
                                    <span class="text-2xl mr-3 ">📥</span> Document Processing
                                </h3>
                                <p class="pl-9 mt-2 text-white ">Extract and process data from invoices, contracts, and forms automatically.</p>
                            </div>

                            <div class="service-selector active " data-service="reporting ">
                                <h3 class="text-xl font-light flex items-center ">
                                    <span class="text-2xl mr-3 ">📊</span> Reporting
                                </h3>
                                <p class="pl-9 mt-2 text-white ">Get real-time insights with automated dashboards and business intelligence.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Right column with demo image -->
                    <div class="demo-right-col hidden lg:block ">
                        <div class="demo-browser-container ">
                            <div class="bg-white rounded-lg shadow-xl overflow-hidden browser-window ">
                                <!-- Browser Chrome -->
                                <div class="bg-gray-100 px-4 py-2 flex items-center border-b ">
                                    <div class="flex space-x-2 ">
                                        <div class="w-3 h-3 bg-red-500 rounded-full "></div>
                                        <div class="w-3 h-3 bg-yellow-500 rounded-full "></div>
                                        <div class="w-3 h-3 bg-green-500 rounded-full "></div>
                                    </div>
                                    <div class="ml-4 flex-1 text-center ">
                                        <span class="text-xs text-gray-500 ">automation.sahlasolutions.com</span>
                                    </div>
                                </div>

                                <!-- Content Area -->
                                <div class="service-image-container ">
                                    <!-- Chatbots Demo -->
                                    <div class="service-image " id="chatbots-image " style="display: none; ">
                                        <img src="../assets/images/instagramchat.webp" alt="AI Chatbot Dashboard " class="w-full ">
                                    </div>

                                    <!-- Scheduling Demo -->
                                    <div class="service-image " id="scheduling-image " style="display: none; ">
                                        <img src="../assets/images/aischedule.png" alt="Smart Scheduling Dashboard " class="w-full ">
                                    </div>

                                    <!-- Documents Demo -->
                                    <div class="service-image " id="documents-image " style="display: none; ">
                                        <img src="../assets/images/document-dashboard.png" alt="Document Processing Dashboard " class="w-full ">
                                    </div>

                                    <!-- Reporting Demo -->
                                    <div class="service-image " id="reporting-image " style="display: block; ">
                                        <img src="../assets/images/aianalytics.png" alt="Reporting Dashboard " class="w-full ">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Mobile version of the demo image -->
                    <div class="mt-12 lg:hidden ">
                        <div class="bg-white rounded-lg shadow-xl overflow-hidden browser-window ">
                            <!-- Browser Chrome -->
                            <div class="bg-gray-100 px-4 py-2 flex items-center border-b ">
                                <div class="flex space-x-2 ">
                                    <div class="w-3 h-3 bg-red-500 rounded-full "></div>
                                    <div class="w-3 h-3 bg-yellow-500 rounded-full "></div>
                                    <div class="w-3 h-3 bg-green-500 rounded-full "></div>
                                </div>
                                <div class="ml-4 flex-1 text-center ">
                                    <span class="text-xs text-gray-500 ">automation.sahlasolutions.com</span>
                                </div>
                            </div>

                            <!-- Content Area -->
                            <div class="service-image-container ">
                                <!-- Chatbots Demo (Mobile) -->
                                <div class="service-image " id="chatbots-image-mobile " style="display: none; ">
                                    <img src="../assets/images/instagramchat.webp" alt="AI Chatbot Dashboard " class="w-full ">
                                </div>

                                <!-- Scheduling Demo (Mobile) -->
                                <div class="service-image " id="scheduling-image-mobile " style="display: none; ">
                                    <img src="../assets/images/aischedule.png" alt="Smart Scheduling Dashboard " class="w-full ">
                                </div>

                                <!-- Documents Demo (Mobile) -->
                                <div class="service-image " id="documents-image-mobile " style="display: none; ">
                                    <img src="../assets/images/document-dashboard.png" alt="Document Processing Dashboard " class="w-full ">
                                </div>

                                <!-- Reporting Demo (Mobile) -->
                                <div class="service-image " id="reporting-image-mobile " style="display: block; ">
                                    <img src="../assets/images/aianalytics.png" alt="Reporting Dashboard " class="w-full ">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Featured Automation Services -->
        <section class="py-24 bg-white " id="use-cases ">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8 ">
                <div class="mx-auto max-w-2xl lg:text-center mb-16 ">
                    <p class="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl ">Everything you need to automate your business.</p>
                    <p class="mt-6 text-lg leading-8 text-gray-400 ">Our most sought-after automation offerings, built for immediate value to free your team from repetitive tasks.</p>
                </div>

                <div class="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none ">
                    <dl class="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3 ">
                        <!-- AI-Powered Chatbots -->
                        <div class="flex flex-col ">
                            <dt class="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900 ">
                                <div class="h-10 w-10 flex items-center justify-center rounded-lg bg-primary-100 ">
                                    <span class="text-primary text-lg ">🤖</span>
                                </div>
                                <span>AI-Powered Chatbots</span>
                            </dt>
                            <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-400 ">
                                <p class="flex-auto ">Automate sales inquiries, support tickets, and lead qualification with intelligent conversational agents.</p>
                                <p class="mt-6 ">
                                    <span class="font-medium text-gray-900 ">Integrate with</span> WhatsApp, Messenger, Instagram, or your website.
                                </p>
                                <p class="mt-6 ">
                                    <span class="font-medium text-primary ">Avg. ROI:</span> 60% reduction in response time, 24/7 availability.
                                </p>
                            </dd>
                        </div>

                        <!-- Smart Scheduling -->
                        <div class="flex flex-col ">
                            <dt class="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900 ">
                                <div class="h-10 w-10 flex items-center justify-center rounded-lg bg-primary-100 ">
                                    <span class="text-primary text-lg ">📅</span>
                                </div>
                                <span>Smart Scheduling & CRM Sync</span>
                            </dt>
                            <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-400 ">
                                <p class="flex-auto ">Automatically book meetings, send reminders, and update your CRM entries without manual work.</p>
                                <p class="mt-6 ">
                                    <span class="font-medium text-gray-900 ">Compatible with</span> Google Calendar, HubSpot, Zoho, and more.
                                </p>
                                <p class="mt-6 ">
                                    <span class="font-medium text-primary ">Avg. Time Saved:</span> 12–15 hours per employee/month.
                                </p>
                            </dd>
                        </div>

                        <!-- Social Media Content Generation -->
                        <div class="flex flex-col ">
                            <dt class="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900 ">
                                <div class="h-10 w-10 flex items-center justify-center rounded-lg bg-primary-100 ">
                                    <span class="text-primary text-lg ">✍️</span>
                                </div>
                                <span>Social Media Automation</span>
                            </dt>
                            <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-400 ">
                                <p class="flex-auto ">Auto-generate captions, images, and video scripts from your brand tone and schedule across platforms.</p>
                                <p class="mt-6 ">
                                    <span class="font-medium text-gray-900 ">Powered by</span> LLMs for consistent quality and contextual relevance.
                                </p>
                                <p class="mt-6 ">
                                    <span class="font-medium text-primary ">Result:</span> More posts, higher engagement, zero burnout.
                                </p>
                            </dd>
                        </div>

                        <!-- Document & Data Workflow -->
                        <div class="flex flex-col ">
                            <dt class="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900 ">
                                <div class="h-10 w-10 flex items-center justify-center rounded-lg bg-primary-100 ">
                                    <span class="text-primary text-lg ">📥</span>
                                </div>
                                <span>Document Workflow Automation</span>
                            </dt>
                            <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-400 ">
                                <p class="flex-auto ">Process PDFs, invoices, and contracts with AI-based data extraction and automated workflows.</p>
                                <p class="mt-6 ">
                                    <span class="font-medium text-gray-900 ">Ideal for</span> HR, logistics, legal, and admin-heavy teams.
                                </p>
                                <p class="mt-6 ">
                                    <span class="font-medium text-primary ">Example:</span> One client saved 100+ hours monthly in manual entry.
                                </p>
                            </dd>
                        </div>

                        <!-- Business Intelligence Dashboards -->
                        <div class="flex flex-col ">
                            <dt class="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900 ">
                                <div class="h-10 w-10 flex items-center justify-center rounded-lg bg-primary-100 ">
                                    <span class="text-primary text-lg ">📈</span>
                                </div>
                                <span>Real-time BI Dashboards</span>
                            </dt>
                            <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-400 ">
                                <p class="flex-auto ">Automatically collect, transform, and visualize your business KPIs for better decision-making.</p>
                                <p class="mt-6 ">
                                    <span class="font-medium text-gray-900 ">Pulls data from</span> Notion, Airtable, Shopify, and ERPs.
                                </p>
                                <p class="mt-6 ">
                                    <span class="font-medium text-primary ">Benefit:</span> Smarter decisions, zero spreadsheet chaos.
                                </p>
                            </dd>
                        </div>

                        <!-- VAT Handling -->
                        <div class="flex flex-col ">
                            <dt class="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900 ">
                                <div class="h-10 w-10 flex items-center justify-center rounded-lg bg-primary-100 ">
                                    <span class="text-primary text-lg ">🏢</span>
                                </div>
                                <span>Custom Enterprise Integrations</span>
                            </dt>
                            <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-400 ">
                                <p class="flex-auto ">Connect your legacy systems with modern tools through custom API development and data pipelines.</p>
                                <p class="mt-6 ">
                                    <span class="font-medium text-gray-900 ">Works with</span> SAP, Oracle, custom ERPs and proprietary systems.
                                </p>
                                <p class="mt-6 ">
                                    <span class="font-medium text-primary ">ROI:</span> Integration costs recovered within 4-6 months on average.
                                </p>
                            </dd>
                        </div>
                    </dl>
                </div>
            </div>
        </section>

        <!-- Business Case Section -->
        <section class="py-20 bg-light ">
            <div class="container mx-auto px-4 ">
                <div class="max-w-3xl mx-auto text-center mb-16 ">
                    <h2 class="text-3xl sm:text-4xl font-bold mb-4 ">📊 Why Automate? The Business Case</h2>
                    <p class="text-lg text-gray-400 ">Automation is not just about speed — it's about amplifying your team's capacity and decision-making.</p>
                </div>

                <div class="overflow-x-auto ">
                    <table class="stats-table ">
                        <thead>
                            <tr>
                                <th>Benefit</th>
                                <th>Impact Area</th>
                                <th>Avg. ROI</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>⏱️ Time Saved</td>
                                <td>Admin & Ops Teams</td>
                                <td class="text-green-400 font-medium ">30–70%</td>
                            </tr>
                            <tr>
                                <td>🚀 Productivity Boost</td>
                                <td>Sales & Marketing</td>
                                <td class="text-green-400 font-medium ">+45%</td>
                            </tr>
                            <tr>
                                <td>📉 Reduced Errors</td>
                                <td>Finance & Data Entry</td>
                                <td class="text-green-400 font-medium ">-80%</td>
                            </tr>
                            <tr>
                                <td>🧠 Better Decisions</td>
                                <td>Management & Strategy</td>
                                <td class="text-green-400 font-medium ">+60%</td>
                            </tr>
                            <tr>
                                <td>💸 Cost Efficiency</td>
                                <td>Overall Ops</td>
                                <td class="text-green-400 font-medium ">+35%</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </section>

        <!-- Success Stories -->
        <section class="py-20" style="background-color: var(--background-color)">
            <div class="container mx-auto px-4 ">
                <div class="max-w-3xl mx-auto text-center mb-16 ">
                    <h2 class="text-3xl sm:text-4xl font-bold mb-4 ">🟦 Success Stories</h2>
                    <p class="text-lg text-gray-400 ">Real businesses achieving real results with our automation solutions.</p>
                </div>
                <!-- Success Story 1 -->
                <ul role="list" class="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-6 sm:gap-8 lg:mt-20 lg:max-w-none lg:grid-cols-3">
                    <li>
                        <ul role="list" class="flex flex-col gap-y-6 sm:gap-y-8">
                            <li>
                                <figure class="relative rounded-2xl bg-white p-6 shadow-xl shadow-slate-900/10">
                                    <svg aria-hidden="true" width="105" height="78" class="absolute top-6 left-6 fill-slate-100">
                                            <path d="M25.086 77.292c-4.821 0-9.115-1.205-12.882-3.616-3.767-2.561-6.78-6.102-9.04-10.622C1.054 58.534 0 53.411 0 47.686c0-5.273.904-10.396 2.712-15.368 1.959-4.972 4.746-9.567 8.362-13.786a59.042 59.042 0 0 1 12.43-11.3C28.325 3.917 33.599 1.507 39.324 0l11.074 13.786c-6.479 2.561-11.677 5.951-15.594 10.17-3.767 4.219-5.65 7.835-5.65 10.848 0 1.356.377 2.863 1.13 4.52.904 1.507 2.637 3.089 5.198 4.746 3.767 2.41 6.328 4.972 7.684 7.684 1.507 2.561 2.26 5.5 2.26 8.814 0 5.123-1.959 9.19-5.876 12.204-3.767 3.013-8.588 4.52-14.464 4.52Zm54.24 0c-4.821 0-9.115-1.205-12.882-3.616-3.767-2.561-6.78-6.102-9.04-10.622-2.11-4.52-3.164-9.643-3.164-15.368 0-5.273.904-10.396 2.712-15.368 1.959-4.972 4.746-9.567 8.362-13.786a59.042 59.042 0 0 1 12.43-11.3C82.565 3.917 87.839 1.507 93.564 0l11.074 13.786c-6.479 2.561-11.677 5.951-15.594 10.17-3.767 4.219-5.65 7.835-5.65 10.848 0 1.356.377 2.863 1.13 4.52.904 1.507 2.637 3.089 5.198 4.746 3.767 2.41 6.328 4.972 7.684 7.684 1.507 2.561 2.26 5.5 2.26 8.814 0 5.123-1.959 9.19-5.876 12.204-3.767 3.013-8.588 4.52-14.464 4.52Z"></path>
                                        </svg>
                                    <blockquote class="relative">
                                        <p class="text-lg tracking-tight text-slate-900">Partnering with Sahla Smart Solutions on automating our customer service workflows was a game-changer. Call resolution times dropped by 60%, and customer satisfaction hit an all-time high. Their AI integration is
                                            seamless and scalable</p>
                                    </blockquote>
                                    <figcaption class="relative mt-6 flex items-center justify-between border-t border-slate-100 pt-6">
                                        <div>
                                            <div class="font-display text-base text-slate-900">Layla Mostafa</div>
                                            <div class="mt-1 text-sm text-slate-500">Head of Customer Experience, Lacorta, Egypt</div>
                                        </div>
                                        <div class="overflow-hidden rounded-full bg-slate-50">
                                            <img alt="" loading="lazy" width="56" height="56" decoding="async" data-nimg="1" class="h-14 w-14 object-cover" style="color:transparent" srcSet="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar-1.c78616b7.png&amp;w=64&amp;q=75 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar-1.c78616b7.png&amp;w=128&amp;q=75 2x"
                                                src="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar-1.c78616b7.png&amp;w=128&amp;q=75" />
                                        </div>
                                    </figcaption>
                                </figure>
                            </li>
                            <li>
                                <figure class="relative rounded-2xl bg-white p-6 shadow-xl shadow-slate-900/10">
                                    <svg aria-hidden="true" width="105" height="78" class="absolute top-6 left-6 fill-slate-100">
                                            <path d="M25.086 77.292c-4.821 0-9.115-1.205-12.882-3.616-3.767-2.561-6.78-6.102-9.04-10.622C1.054 58.534 0 53.411 0 47.686c0-5.273.904-10.396 2.712-15.368 1.959-4.972 4.746-9.567 8.362-13.786a59.042 59.042 0 0 1 12.43-11.3C28.325 3.917 33.599 1.507 39.324 0l11.074 13.786c-6.479 2.561-11.677 5.951-15.594 10.17-3.767 4.219-5.65 7.835-5.65 10.848 0 1.356.377 2.863 1.13 4.52.904 1.507 2.637 3.089 5.198 4.746 3.767 2.41 6.328 4.972 7.684 7.684 1.507 2.561 2.26 5.5 2.26 8.814 0 5.123-1.959 9.19-5.876 12.204-3.767 3.013-8.588 4.52-14.464 4.52Zm54.24 0c-4.821 0-9.115-1.205-12.882-3.616-3.767-2.561-6.78-6.102-9.04-10.622-2.11-4.52-3.164-9.643-3.164-15.368 0-5.273.904-10.396 2.712-15.368 1.959-4.972 4.746-9.567 8.362-13.786a59.042 59.042 0 0 1 12.43-11.3C82.565 3.917 87.839 1.507 93.564 0l11.074 13.786c-6.479 2.561-11.677 5.951-15.594 10.17-3.767 4.219-5.65 7.835-5.65 10.848 0 1.356.377 2.863 1.13 4.52.904 1.507 2.637 3.089 5.198 4.746 3.767 2.41 6.328 4.972 7.684 7.684 1.507 2.561 2.26 5.5 2.26 8.814 0 5.123-1.959 9.19-5.876 12.204-3.767 3.013-8.588 4.52-14.464 4.52Z"></path>
                                        </svg>
                                    <blockquote class="relative">
                                        <p class="text-lg tracking-tight text-slate-900">Sahla Smart Solutions transformed our entire supply chain operations through intelligent automation. From inventory tracking to fleet scheduling, efficiency improved by over 40%. Their team understood our regional
                                            challenges and delivered beyond expectations.</p>
                                    </blockquote>
                                    <figcaption class="relative mt-6 flex items-center justify-between border-t border-slate-100 pt-6">
                                        <div>
                                            <div class="font-display text-base text-slate-900">Amr Al Mansour</div>
                                            <div class="mt-1 text-sm text-slate-500">Operations Director, EasyGo Logistics Co., UAE</div>
                                        </div>
                                        <div class="overflow-hidden rounded-full bg-slate-50">
                                            <img alt="" loading="lazy" width="56" height="56" decoding="async" data-nimg="1" class="h-14 w-14 object-cover" style="color:transparent" srcSet="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar-4.16b4e29e.png&amp;w=64&amp;q=75 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar-4.16b4e29e.png&amp;w=128&amp;q=75 2x"
                                                src="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar-4.16b4e29e.png&amp;w=128&amp;q=75" />
                                        </div>
                                    </figcaption>
                                </figure>
                            </li>
                        </ul>
                    </li>
                    <li>
                        <ul role="list" class="flex flex-col gap-y-6 sm:gap-y-8">
                            <li>
                                <figure class="relative rounded-2xl bg-white p-6 shadow-xl shadow-slate-900/10">
                                    <svg aria-hidden="true" width="105" height="78" class="absolute top-6 left-6 fill-slate-100">
                                            <path d="M25.086 77.292c-4.821 0-9.115-1.205-12.882-3.616-3.767-2.561-6.78-6.102-9.04-10.622C1.054 58.534 0 53.411 0 47.686c0-5.273.904-10.396 2.712-15.368 1.959-4.972 4.746-9.567 8.362-13.786a59.042 59.042 0 0 1 12.43-11.3C28.325 3.917 33.599 1.507 39.324 0l11.074 13.786c-6.479 2.561-11.677 5.951-15.594 10.17-3.767 4.219-5.65 7.835-5.65 10.848 0 1.356.377 2.863 1.13 4.52.904 1.507 2.637 3.089 5.198 4.746 3.767 2.41 6.328 4.972 7.684 7.684 1.507 2.561 2.26 5.5 2.26 8.814 0 5.123-1.959 9.19-5.876 12.204-3.767 3.013-8.588 4.52-14.464 4.52Zm54.24 0c-4.821 0-9.115-1.205-12.882-3.616-3.767-2.561-6.78-6.102-9.04-10.622-2.11-4.52-3.164-9.643-3.164-15.368 0-5.273.904-10.396 2.712-15.368 1.959-4.972 4.746-9.567 8.362-13.786a59.042 59.042 0 0 1 12.43-11.3C82.565 3.917 87.839 1.507 93.564 0l11.074 13.786c-6.479 2.561-11.677 5.951-15.594 10.17-3.767 4.219-5.65 7.835-5.65 10.848 0 1.356.377 2.863 1.13 4.52.904 1.507 2.637 3.089 5.198 4.746 3.767 2.41 6.328 4.972 7.684 7.684 1.507 2.561 2.26 5.5 2.26 8.814 0 5.123-1.959 9.19-5.876 12.204-3.767 3.013-8.588 4.52-14.464 4.52Z"></path>
                                        </svg>
                                    <blockquote class="relative">
                                        <p class="text-lg tracking-tight text-slate-900">Thanks to Sahla’s AI-powered automation, we slashed manual onboarding time for new merchants by 70%. Their expertise in fintech automation helped us scale faster while remaining fully compliant with regulatory standards</p>
                                    </blockquote>
                                    <figcaption class="relative mt-6 flex items-center justify-between border-t border-slate-100 pt-6">
                                        <div>
                                            <div class="font-display text-base text-slate-900">Tarek Haddad</div>
                                            <div class="mt-1 text-sm text-slate-500">Founder of GoodPay, Jordan</div>
                                        </div>
                                        <div class="overflow-hidden rounded-full bg-slate-50">
                                            <img alt="" loading="lazy" width="56" height="56" decoding="async" data-nimg="1" class="h-14 w-14 object-cover" style="color:transparent" srcSet="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar-5.e7f7faf2.png&amp;w=64&amp;q=75 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar-5.e7f7faf2.png&amp;w=128&amp;q=75 2x"
                                                src="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar-5.e7f7faf2.png&amp;w=128&amp;q=75" />
                                        </div>
                                    </figcaption>
                                </figure>
                            </li>
                            <li>
                                <figure class="relative rounded-2xl bg-white p-6 shadow-xl shadow-slate-900/10">
                                    <svg aria-hidden="true" width="105" height="78" class="absolute top-6 left-6 fill-slate-100">
                                            <path d="M25.086 77.292c-4.821 0-9.115-1.205-12.882-3.616-3.767-2.561-6.78-6.102-9.04-10.622C1.054 58.534 0 53.411 0 47.686c0-5.273.904-10.396 2.712-15.368 1.959-4.972 4.746-9.567 8.362-13.786a59.042 59.042 0 0 1 12.43-11.3C28.325 3.917 33.599 1.507 39.324 0l11.074 13.786c-6.479 2.561-11.677 5.951-15.594 10.17-3.767 4.219-5.65 7.835-5.65 10.848 0 1.356.377 2.863 1.13 4.52.904 1.507 2.637 3.089 5.198 4.746 3.767 2.41 6.328 4.972 7.684 7.684 1.507 2.561 2.26 5.5 2.26 8.814 0 5.123-1.959 9.19-5.876 12.204-3.767 3.013-8.588 4.52-14.464 4.52Zm54.24 0c-4.821 0-9.115-1.205-12.882-3.616-3.767-2.561-6.78-6.102-9.04-10.622-2.11-4.52-3.164-9.643-3.164-15.368 0-5.273.904-10.396 2.712-15.368 1.959-4.972 4.746-9.567 8.362-13.786a59.042 59.042 0 0 1 12.43-11.3C82.565 3.917 87.839 1.507 93.564 0l11.074 13.786c-6.479 2.561-11.677 5.951-15.594 10.17-3.767 4.219-5.65 7.835-5.65 10.848 0 1.356.377 2.863 1.13 4.52.904 1.507 2.637 3.089 5.198 4.746 3.767 2.41 6.328 4.972 7.684 7.684 1.507 2.561 2.26 5.5 2.26 8.814 0 5.123-1.959 9.19-5.876 12.204-3.767 3.013-8.588 4.52-14.464 4.52Z"></path>
                                        </svg>
                                    <blockquote class="relative">
                                        <p class="text-lg tracking-tight text-slate-900">Implementing AI-driven HR automation with Sahla Smart Solutions significantly reduced our recruitment cycle and improved candidate matching accuracy. Their solution is intuitive and tailored perfectly for the MENA
                                            workforce landscape.</p>
                                    </blockquote>
                                    <figcaption class="relative mt-6 flex items-center justify-between border-t border-slate-100 pt-6">
                                        <div>
                                            <div class="font-display text-base text-slate-900">Badr Al Jaber</div>
                                            <div class="mt-1 text-sm text-slate-500">HR Director, Gulf PetroTech, Bahrain</div>
                                        </div>
                                        <div class="overflow-hidden rounded-full bg-slate-50">
                                            <img alt="" loading="lazy" width="56" height="56" decoding="async" data-nimg="1" class="h-14 w-14 object-cover" style="color:transparent" srcSet="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar-2.c72e5a40.png&amp;w=64&amp;q=75 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar-2.c72e5a40.png&amp;w=128&amp;q=75 2x"
                                                src="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar-2.c72e5a40.png&amp;w=128&amp;q=75" />
                                        </div>
                                    </figcaption>
                                </figure>
                            </li>
                        </ul>
                    </li>
                    <li>
                        <ul role="list" class="flex flex-col gap-y-6 sm:gap-y-8">
                            <li>
                                <figure class="relative rounded-2xl bg-white p-6 shadow-xl shadow-slate-900/10">
                                    <svg aria-hidden="true" width="105" height="78" class="absolute top-6 left-6 fill-slate-100">
                                            <path d="M25.086 77.292c-4.821 0-9.115-1.205-12.882-3.616-3.767-2.561-6.78-6.102-9.04-10.622C1.054 58.534 0 53.411 0 47.686c0-5.273.904-10.396 2.712-15.368 1.959-4.972 4.746-9.567 8.362-13.786a59.042 59.042 0 0 1 12.43-11.3C28.325 3.917 33.599 1.507 39.324 0l11.074 13.786c-6.479 2.561-11.677 5.951-15.594 10.17-3.767 4.219-5.65 7.835-5.65 10.848 0 1.356.377 2.863 1.13 4.52.904 1.507 2.637 3.089 5.198 4.746 3.767 2.41 6.328 4.972 7.684 7.684 1.507 2.561 2.26 5.5 2.26 8.814 0 5.123-1.959 9.19-5.876 12.204-3.767 3.013-8.588 4.52-14.464 4.52Zm54.24 0c-4.821 0-9.115-1.205-12.882-3.616-3.767-2.561-6.78-6.102-9.04-10.622-2.11-4.52-3.164-9.643-3.164-15.368 0-5.273.904-10.396 2.712-15.368 1.959-4.972 4.746-9.567 8.362-13.786a59.042 59.042 0 0 1 12.43-11.3C82.565 3.917 87.839 1.507 93.564 0l11.074 13.786c-6.479 2.561-11.677 5.951-15.594 10.17-3.767 4.219-5.65 7.835-5.65 10.848 0 1.356.377 2.863 1.13 4.52.904 1.507 2.637 3.089 5.198 4.746 3.767 2.41 6.328 4.972 7.684 7.684 1.507 2.561 2.26 5.5 2.26 8.814 0 5.123-1.959 9.19-5.876 12.204-3.767 3.013-8.588 4.52-14.464 4.52Z"></path>
                                        </svg>
                                    <blockquote class="relative">
                                        <p class="text-lg tracking-tight text-slate-900">Sahla Smart Solutions automated our document management and legal research workflows using natural language AI. The impact was immediate—our lawyers now spend less time on admin and more time with clients.</p>
                                    </blockquote>
                                    <figcaption class="relative mt-6 flex items-center justify-between border-t border-slate-100 pt-6">
                                        <div>
                                            <div class="font-display text-base text-slate-900">Samir Jabr</div>
                                            <div class="mt-1 text-sm text-slate-500">Managing Partner, Atlas Legal Consultants, Jordan</div>
                                        </div>
                                        <div class="overflow-hidden rounded-full bg-slate-50">
                                            <img alt="" loading="lazy" width="56" height="56" decoding="async" data-nimg="1" class="h-14 w-14 object-cover" style="color:transparent" srcSet="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar-3.eaa9ef6f.png&amp;w=64&amp;q=75 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar-3.eaa9ef6f.png&amp;w=128&amp;q=75 2x"
                                                src="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar-3.eaa9ef6f.png&amp;w=128&amp;q=75" />
                                        </div>
                                    </figcaption>
                                </figure>
                            </li>
                            <li>
                                <figure class="relative rounded-2xl bg-white p-6 shadow-xl shadow-slate-900/10">
                                    <svg aria-hidden="true" width="105" height="78" class="absolute top-6 left-6 fill-slate-100">
                                            <path d="M25.086 77.292c-4.821 0-9.115-1.205-12.882-3.616-3.767-2.561-6.78-6.102-9.04-10.622C1.054 58.534 0 53.411 0 47.686c0-5.273.904-10.396 2.712-15.368 1.959-4.972 4.746-9.567 8.362-13.786a59.042 59.042 0 0 1 12.43-11.3C28.325 3.917 33.599 1.507 39.324 0l11.074 13.786c-6.479 2.561-11.677 5.951-15.594 10.17-3.767 4.219-5.65 7.835-5.65 10.848 0 1.356.377 2.863 1.13 4.52.904 1.507 2.637 3.089 5.198 4.746 3.767 2.41 6.328 4.972 7.684 7.684 1.507 2.561 2.26 5.5 2.26 8.814 0 5.123-1.959 9.19-5.876 12.204-3.767 3.013-8.588 4.52-14.464 4.52Zm54.24 0c-4.821 0-9.115-1.205-12.882-3.616-3.767-2.561-6.78-6.102-9.04-10.622-2.11-4.52-3.164-9.643-3.164-15.368 0-5.273.904-10.396 2.712-15.368 1.959-4.972 4.746-9.567 8.362-13.786a59.042 59.042 0 0 1 12.43-11.3C82.565 3.917 87.839 1.507 93.564 0l11.074 13.786c-6.479 2.561-11.677 5.951-15.594 10.17-3.767 4.219-5.65 7.835-5.65 10.848 0 1.356.377 2.863 1.13 4.52.904 1.507 2.637 3.089 5.198 4.746 3.767 2.41 6.328 4.972 7.684 7.684 1.507 2.561 2.26 5.5 2.26 8.814 0 5.123-1.959 9.19-5.876 12.204-3.767 3.013-8.588 4.52-14.464 4.52Z"></path>
                                        </svg>
                                    <blockquote class="relative">
                                        <p class="text-lg tracking-tight text-slate-900">We collaborated with Sahla to automate our inventory forecasting and dynamic pricing models. The result? A 35% reduction in overstock and significant margin improvements. Their AI strategy paid measurable dividends.</p>
                                    </blockquote>
                                    <figcaption class="relative mt-6 flex items-center justify-between border-t border-slate-100 pt-6">
                                        <div>
                                            <div class="font-display text-base text-slate-900">Amy Hahn</div>
                                            <div class="mt-1 text-sm text-slate-500">Director at Walmart</div>
                                        </div>
                                        <div class="overflow-hidden rounded-full bg-slate-50">
                                            <img alt="" loading="lazy" width="56" height="56" decoding="async" data-nimg="1" class="h-14 w-14 object-cover" style="color:transparent" srcSet="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar-4.16b4e29e.png&amp;w=64&amp;q=75 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar-4.16b4e29e.png&amp;w=128&amp;q=75 2x"
                                                src="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Favatar-4.16b4e29e.png&amp;w=128&amp;q=75" />
                                        </div>
                                    </figcaption>
                                </figure>
                            </li>
                        </ul>
                    </li>
                </ul>

            </div>
        </section>

        <!-- CTA Section -->
        <section class="py-20 bg-primary text-white ">
            <div class="container mx-auto px-4 text-center ">
                <div class="max-w-3xl mx-auto ">
                    <h2 class="text-3xl sm:text-4xl font-bold mb-6 ">📬 Let's Automate Your Growth</h2>
                    <p class="text-xl mb-10 ">Whether you're a solo founder or a growing enterprise — we'll design, implement, and maintain a tailored automation solution for your team.</p>
                    <div class="flex flex-col sm:flex-row gap-4 justify-center ">
                        <a href="contact.html " class="button button-white ">Book Your Discovery Call</a>
                        <a href="# " class="button button-outline-white ">See Pricing Options</a>
                    </div>
                </div>
            </div>
        </section>

        <!-- FAQ Section -->
        <section class="py-24 faq-section ">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8 ">
                <div class="mx-auto max-w-3xl lg:text-center mb-16 ">
                    <h2 class="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl ">Frequently asked questions</h2>
                    <p class="mt-4 text-lg text-gray-400 ">
                        If you can't find what you're looking for, email our support team and if you're lucky someone will get back to you.
                    </p>
                </div>

                <div class="faq-columns max-w-4xl mx-auto ">
                    <!-- Left Column -->
                    <div>
                        <!-- Question 1 -->
                        <div class="faq-item ">
                            <h3 class="faq-question ">What platforms do you support?</h3>
                            <div class="faq-answer ">
                                <p>We support integrations with over 300 apps, including Google Workspace, Slack, Notion, Shopify, Airtable, and any API-ready platform.</p>
                            </div>
                        </div>

                        <!-- Question 2 -->
                        <div class="faq-item ">
                            <h3 class="faq-question ">Are these automations custom-built or template-based?</h3>
                            <div class="faq-answer ">
                                <p>Both. We offer ready-made automation templates and also design fully custom workflows based on your business logic.</p>
                            </div>
                        </div>

                        <!-- Question 3 -->
                        <div class="faq-item ">
                            <h3 class="faq-question ">Do I need to be technical to use this?</h3>
                            <div class="faq-answer ">
                                <p>Not at all. We handle the entire setup, documentation, and support. Your team simply enjoys the results.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Right Column -->
                    <div>
                        <!-- Question 4 -->
                        <div class="faq-item ">
                            <h3 class="faq-question ">What's the typical onboarding process?</h3>
                            <div class="faq-answer ">
                                <p>Week 1: Discovery</p>
                                <p>Week 2: Architecture & Prototype</p>
                                <p>Week 3: Testing & Launch</p>
                                <p>Ongoing: Monitoring + Optimization</p>
                            </div>
                        </div>

                        <!-- Question 5 -->
                        <div class="faq-item ">
                            <h3 class="faq-question ">How much does it cost?</h3>
                            <div class="faq-answer ">
                                <p>Pricing varies by complexity and use-case. We offer affordable tiers for startups and scalable options for enterprises.</p>
                            </div>
                        </div>

                        <!-- Question 6 -->
                        <div class="faq-item ">
                            <h3 class="faq-question ">Can we expect more automation features?</h3>
                            <div class="faq-answer ">
                                <p>Yes, we're constantly adding new integrations and capabilities. Our roadmap is guided by client needs and industry trends.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- f -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <!-- Company Info Column -->
                <div class="footer-info">
                    <a href="../index.html" class="footer-logo">
                        <img src="../assets/images/Negative Horizontal Version.svg" alt="Sahla Smart Solutions" class="logo-img" id="foot-logo">
                    </a>
                    <p class="footer-description">Empowering businesses through innovative venture building and automation solutions in the MENA region.</p>
                    <div class="footer-social">
                        <a href="https://eg.linkedin.com/company/sahla-solutions" target="_blank" rel="noopener noreferrer"><i class="ri-linkedin-fill"></i></a>
                        <a href="https://x.com/sahlasolutions" target="_blank" rel="noopener noreferrer"><i class="ri-twitter-fill"></i></a>
                        <a href="https://www.facebook.com/SahlaSmartSolutions/" target="_blank" rel="noopener noreferrer"><i class="ri-facebook-fill"></i></a>
                        <a href="https://www.instagram.com/sahlasolutions/" target="_blank" rel="noopener noreferrer"><i class="ri-instagram-fill"></i></a>
                    </div>
                </div>

                <!-- Quick Links Column -->
                <div class="footer-links-column">
                    <h4>Quick Links</h4>
                    <ul class="footer-links-list">
                        <li><a href="../index.html">Home</a></li>
                        <li><a href="./about.html">About Us</a></li>
                        <li><a href="./ventures.html">Our Ventures</a></li>
                        <li><a href="./partners.html">Partners</a></li>
                        <li><a href="./careers.html">Careers</a></li>
                        <li><a href="./contact.html">Contact Us</a></li>
                    </ul>
                </div>

                <!-- Services Column -->
                <div class="footer-links-column">
                    <h4>Services</h4>
                    <ul class="footer-links-list">
                        <li><a href="./process-automation.html">Process Automation</a></li>
                        <li><a href="./technology-consulting.html">Technology Consulting</a></li>
                    </ul>
                </div>

                <!-- Newsletter Column -->
                <div class="footer-newsletter">
                    <h4>Newsletter</h4>

                    <form class="mb-4">
                        <div class="flex">
                            <!-- <input type="email" placeholder="Your email" class="w-full px-4 py-2 bg-gray-800 border-none text-white text-sm rounded-l"> -->
                            <a href="javascript:void(0)" class="ml-onclick-form" onclick="ml('show', 'gturk0', true)" style="
                                display: inline-block;
                                padding: 8px 16px;
                                background-color: var(--primary-color-1);
                                color: white;
                                font-size: 16px;
                                font-family: sans-serif;
                                border: none;
                                border-radius: 6px;
                                text-decoration: none;
                                cursor: pointer;
                                transition: background-color 0.3s ease;
                                " onmouseover="this.style.backgroundColor='var(--primary-color-2)'" onmouseout="this.style.backgroundColor= 'var(--primary-color-3)'">
                                Subscribe to Newsletter
                                </a>
                        </div>
                    </form>
                    <p>Subscribe to our newsletter to stay updated with our latest news and insights.</p>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-12 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <p class="text-gray-400 text-sm mb-4 md:mb-0">© 2025 Sahla Smart Solutions. All rights reserved.</p>
                    <div class="flex space-x-6">
                        <a href="../pages/privacy-policy.html" class="text-gray-400 hover:text-white text-sm">Privacy Policy</a>
                        <a href="../pages/terms-of-service.html" class="text-gray-400 hover:text-white text-sm">Terms of Service</a>
                        <a href="../pages/cookies-policy.html" class="text-gray-400 hover:text-white text-sm">Cookies Policy</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Theme switch functionality
        const themeSwitch = document.getElementById('theme-switch');
        const html = document.documentElement;
        const navLogo = document.getElementById('nav-logo');
        const footLogo = document.getElementById('foot-logo');

        // Function to set and save theme
        const setTheme = (theme) => {
            html.setAttribute('data-theme', theme);
            localStorage.setItem('theme', theme);
            themeSwitch.classList.toggle('dark', theme === 'dark');

            // Update logo based on theme
            if (theme === 'dark') {
                navLogo.src = '../assets/images/Dark Horizontal Version.svg';
                footLogo.src = '../assets/images/Dark Horizontal Version.svg';
            } else {
                navLogo.src = '../assets/images/Main Horizontal Version.svg';
                footLogo.src = '../assets/images/Main Horizontal Version.svg';
            }
        };

        // Mobile menu toggle
        const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
        const navLinks = document.querySelector('.nav-links');

        mobileMenuToggle.addEventListener('change', function() {
            if (this.checked) {
                navLinks.style.display = 'flex';
            } else {
                navLinks.style.display = '';
            }
        });

        // Set the correct theme class on icon container and logo after load
        document.addEventListener('DOMContentLoaded', () => {
            const currentTheme = html.getAttribute('data-theme');
            themeSwitch.classList.toggle('dark', currentTheme === 'dark');

            // Set initial logo based on theme
            if (currentTheme === 'dark') {
                navLogo.src = '../assets/images/Dark Horizontal Version.svg';
                footLogo.src = '../assets/images/Dark Horizontal Version.svg';
            } else {
                navLogo.src = '../assets/images/Main Horizontal Version.svg';
                footLogo.src = '../assets/images/Main Horizontal Version.svg';
            }

            // Service selector functionality
            const serviceSelectors = document.querySelectorAll('.service-selector');

            serviceSelectors.forEach(selector => {
                selector.addEventListener('click', () => {
                    // Remove active class from all selectors
                    serviceSelectors.forEach(s => s.classList.remove('active'));

                    // Add active class to clicked selector
                    selector.classList.add('active');

                    // Get the service type
                    const service = selector.getAttribute('data-service');

                    // Hide all images (desktop)
                    document.querySelectorAll('.service-image').forEach(img => {
                        img.style.display = 'none';
                    });

                    // Show the selected service image (desktop)
                    const desktopImage = document.getElementById(`${service}-image`);
                    if (desktopImage) desktopImage.style.display = 'block';

                    // Show the selected service image (mobile)
                    const mobileImage = document.getElementById(`${service}-image-mobile`);
                    if (mobileImage) mobileImage.style.display = 'block';
                });
            });
        });

        // Toggle theme on click
        themeSwitch.addEventListener('click', () => {
            const isCurrentlyDark = html.getAttribute('data-theme') === 'dark';
            const newTheme = isCurrentlyDark ? 'light' : 'dark';
            setTheme(newTheme);
        });
    </script>
</body>

</html>