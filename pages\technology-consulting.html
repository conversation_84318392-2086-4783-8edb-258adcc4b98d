<!DOCTYPE html>
<html lang="en">

<head>     <link rel="icon" type="image/x-icon" href="../assets/images/favicon.ico?v=2">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Technology Consulting Services - Sahla Smart Solutions</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#0056b3',
                        secondary: '#4dabf7'
                    },
                    borderRadius: {
                        'none': '0px',
                        'sm': '4px',
                        DEFAULT: '8px',
                        'md': '12px',
                        'lg': '16px',
                        'xl': '20px',
                        '2xl': '24px',
                        '3xl': '32px',
                        'full': '9999px',
                        'button': '8px'
                    }
                }
            }
        }
    </script>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/navigation.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;400;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@4.5.0/fonts/remixicon.css" rel="stylesheet">
    <!-- MailerLite Universal -->
    <script>
        (function(w, d, e, u, f, l, n) {
            w[f] = w[f] || function() {
                    (w[f].q = w[f].q || [])
                    .push(arguments);
                }, l = d.createElement(e), l.async = 1, l.src = u,
                n = d.getElementsByTagName(e)[0], n.parentNode.insertBefore(l, n);
        })
        (window, document, 'script', 'https://assets.mailerlite.com/js/universal.js', 'ml');
        ml('account', '1446707');
    </script>
    <!-- End MailerLite Universal -->
    <style>
         :where([class^="ri-"])::before {
            content: "\f3c2";
        }
        
        .text {
            color: var(--text-color);
        }
        
        .hero-section1 {
            padding: 13%;
            background-size: cover;
            background-position: center;
            background-color: var(--background-color);
        }
        
        .button-bg {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
            font-weight: 600;
            border-radius: 9999px;
            background-color: var(--primary-color-1);
            color: #ffffff;
            outline: none;
        }
        
        .button-bg:hover {
            background-color: #334155;
            color: #f1f5f9;
        }
        
        .button-bg:active {
            background-color: #1e293b;
            color: #cbd5e1;
        }
        
        .button-bg:focus-visible {
            outline: 2px solid #0f172a;
            outline-offset: 2px;
        }
        
        .button-bg-outline {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
            font-weight: 600;
            border-radius: 9999px;
            background-color: transparent;
            border: 1px solid var(--text-color);
        }
        
        .button-bg-outline:hover {
            color: var(--primary-color-1);
            border: 1px solid var(--text-color);
        }
        
        .highlight {
            color: var(--primary-color-1);
            position: relative;
            display: inline-block;
        }
        
        .highlight::after {
            content: '';
            position: absolute;
            left: 0;
            bottom: 0.2em;
            width: 100%;
            height: 0.6em;
            background-repeat: no-repeat;
            background-size: 100% 100%;
            pointer-events: none;
        }
        
        .service-card {
            border-radius: 1rem;
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .service-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        .process-step-card {
            background-color: var(--background-color);
        }        
        
        .process-step {
            position: relative;
            padding-left: 2.5rem;
        }

        
        .process-step::before {
            content: attr(data-step);
            position: absolute;
            left: 0;
            top: 0;
            width: 2rem;
            height: 2rem;
            border-radius: 50%;
            background-color: var(--primary-color-1);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
        
        .pricing-card {
            border-radius: 1rem;
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
            height: 100%;
        }
        
        .pricing-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }
        
        .pricing-card-content {
            flex: 1;
        }
        
        .pricing-card-footer {
            margin-top: auto;
        }
        
        .featured-card {
            transform: scale(1.1);
            border: 2px solid var(--primary-color-1);
            box-shadow: 0 10px 25px rgba(0, 86, 179, 0.15);
            position: relative;
            z-index: 90;
            margin-top: 1rem;
            padding-top: 1.5rem;
        }
        
        .featured-card:hover {
            transform: scale(1.1) translateY(-5px);
        }
        
        .featured-tag {
            position: absolute;
            top: -1rem;
            left: 50%;
            transform: translateX(-50%);
            z-index: 91;
        }
        
        .pricing-table {
            width: 100%;
            border-collapse: collapse;
            margin: 2rem 0;
        }
        
        .pricing-table th,
        .pricing-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }
        
        .pricing-table th {
            background-color: var(--background-color-light);
            font-weight: 600;
        }
        
        .bg-primary-100 {
            background-color: rgba(0, 86, 179, 0.1);
        }
    </style>
</head>

<body class="bg-white">
    <!-- Header & Navigation -->
     <header class="header">
    <div class="container">
      <nav class="nav">
        <a href="../index.html" class="nav-logo">
          <img src="../assets/images/Main Horizontal Version.svg" alt="Sahla Logo" class="logo-img" id="nav-logo">
        </a>
        <input type="checkbox" id="mobile-menu-toggle" class="mobile-menu-toggle">
        <label for="mobile-menu-toggle" class="mobile-menu-button">
          <span></span>
          <span></span>
          <span></span>
        </label>
        <div class="nav-links">
          <div class="dropdown">
            <a href="#services" class="nav-link">Services</a>
            <div class="dropdown-content">
              <a href="./process-automation.html" class="dropdown-link">Process Automation</a>
              <a href="./technology-consulting.html" class="dropdown-link">Technology Consulting</a>
            </div>
          </div>
          <a href="./ventures.html" class="nav-link">Our Ventures</a>
          <a href="./partners.html" class="nav-link">Partners</a>
          <a href="./careers.html" class="nav-link">Careers</a>
          <a href="./blog.html" class="nav-link">Blog</a>
          <a href="./about.html" class="nav-link">About us</a>
          <div class="px-4">
            <div class="h-6 border-l-2 border-gray-300"></div>
          </div>
          <a href="./contact.html" class="px-6 py-2 !rounded-button hover:bg-opacity-90 transition-all" style="background-color: var(--background-color-light); color: var(--text-color);">Contact Sales</a>
          <div class="hidden md:flex items-center gap-4">
            <div class="theme-switch" id="theme-switch">
              <i class="ri-moon-line moon-icon"></i>
              <i class="ri-sun-line sun-icon"></i>
              <script>
                (() => {
                  const savedTheme = localStorage.getItem('theme');
                  const preferred = savedTheme || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                  document.documentElement.setAttribute('data-theme', preferred);
                })();
              </script>
            </div>
          </div>
        </div>
      </nav>
    </div>
  </header>

    <main>
        <!-- Hero Section -->
        <section class="hero-section1 text-center">
            <div class="container mx-auto px-4">
                <div class="max-w-6xl mx-auto">
                    <h1 class="text-7xl sm:text-7xl font-bold mb-6">                        
                        Empowering Growth Through 
                        <span class="relative inline-block">
                            <!-- Scribble SVG -->
                            <svg aria-hidden="true" viewBox="0 0 418 42"
                            class="absolute top-2/3 left-0 w-full h-[0.38em] fill-blue-300/70 z-0 pointer-events-none"
                            preserveAspectRatio="none">
                            <path
                                d="M203.371.916c-26.013-2.078-76.686 1.963-124.73 9.946L67.3 12.749C35.421
                                18.062 18.2 21.766 6.004 25.934 1.244 27.561.828 27.778.874 28.61c.07 1.214.828
                                1.121 9.595-1.176 9.072-2.377 17.15-3.92 39.246-7.496C123.565 7.986 157.869
                                4.492 195.942 5.046c7.461.108 19.25 1.696 19.17 2.582-.107 1.183-7.874
                                4.31-25.75 10.366-21.992 7.45-35.43 12.534-36.701 13.884-2.173
                                2.308-.202 4.407 4.442 4.734 2.654.187 3.263.157 15.593-.78
                                35.401-2.686 57.944-3.488 88.365-3.143 46.327.526 75.721
                                2.23 130.788 7.584 19.787 1.924 20.814 1.98 24.557
                                1.332l.066-.011c1.201-.203 1.53-1.825.399-2.335-2.911-1.31-4.893-1.604-22.048-3.261-57.509-5.556-87.871-7.36-132.059-7.842-23.239-.254-33.617-.116-50.627.674-11.629.54-42.371
                                2.494-46.696 2.967-2.359.259 8.133-3.625 26.504-9.81
                                23.239-7.825 27.934-10.149 28.304-14.005.417-4.348-3.529-6-16.878-7.066Z" />
                            </svg>
                            <!-- Highlighted text -->
                            <span class="relative z-10 text-blue-400">Smart</span>
                        </span>
                        Technology Decisions
                    </h1>
                    <p class="text-xl opacity-90 mb-10 font-light">We help startups, SMEs, and enterprises navigate complex technical challenges, make informed technology choices, and deliver high-impact solutions that align with business goals.</p>
                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        <a href="contact.html" class="button button-bg">Book a Free Discovery Call</a>
                        <a href="#services" class="button button-bg-outline">Explore Our Services</a>
                    </div>
                </div>
            </div>

            <div class="container mx-auto px-4 text-center pt-40">
                <h3 class="h3 text-gray-400 text-base font-medium mb-10">
                    Trusted by technology leaders who make strategic decisions
                </h3>

                <div class="flex flex-wrap justify-center items-center gap-10">
                    <!-- Logo 1 -->
                    <img src="../assets/images/Transistor.svg" alt="Transistor" class="h-8">

                    <!-- Logo 2 -->
                    <img src="../assets/images/Tuple.svg" alt="Tuple" class="h-8">

                    <!-- Logo 3 -->
                    <img src="../assets/images/StaticKit.svg" alt="StaticKit" class="h-8">

                    <!-- Logo 4 -->
                    <img src="../assets/images/Mirage.svg" alt="Mirage" class="h-8">

                    <!-- Logo 5 -->
                    <img src="../assets/images/Laravel.svg" alt="Laravel" class="h-8">

                    <!-- Logo 6 -->
                    <img src="../assets/images/Statamic.svg" alt="Statamic" class="h-8">
                </div>
            </div>
        </section>
        
        <!-- Service Overview Section -->
        <section class="py-24" id="services" style="background-color: var(--background-color-light);">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="mx-auto max-w-2xl lg:text-center mb-16">
                    <h2 class="mt-2 text-3xl font-bold tracking-tight sm:text-4xl">🚀 What We Offer</h2>
                    <p class="mt-6 text-lg leading-8 text-gray-400">Our consulting services span the full spectrum of technology strategy and implementation.</p>
                </div>

                <div class="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
                    <dl class="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3">
                        <!-- Digital Transformation -->
                        <div class="flex flex-col">
                            <dt class="flex items-center gap-x-3 text-base font-semibold leading-7">
                                <div class="h-10 w-10 flex items-center justify-center rounded-lg bg-primary-100">
                                    <span class="text-primary text-lg">📊</span>
                                </div>
                                <span>Digital Transformation & Roadmapping</span>
                            </dt>
                            <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-500">
                                <p class="flex-auto">Create a clear path to digital maturity with strategic roadmaps that align technology initiatives with business objectives.</p>
                            </dd>
                        </div>

                        <!-- Software Architecture -->
                        <div class="flex flex-col">
                            <dt class="flex items-center gap-x-3 text-base font-semibold leading-7">
                                <div class="h-10 w-10 flex items-center justify-center rounded-lg bg-primary-100">
                                    <span class="text-primary text-lg">🏗️</span>
                                </div>
                                <span>Software Architecture & System Design</span>
                            </dt>
                            <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-500">
                                <p class="flex-auto">Design scalable, maintainable software systems that can grow with your business and adapt to changing requirements.</p>
                            </dd>
                        </div>

                        <!-- Cloud Strategy -->
                        <div class="flex flex-col">
                            <dt class="flex items-center gap-x-3 text-base font-semibold leading-7">
                                <div class="h-10 w-10 flex items-center justify-center rounded-lg bg-primary-100">
                                    <span class="text-primary text-lg">☁️</span>
                                </div>
                                <span>Cloud Strategy & Infrastructure Optimization</span>
                            </dt>
                            <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-500">
                                <p class="flex-auto">Optimize your cloud infrastructure for cost, performance, and security while ensuring it aligns with your business needs.</p>
                            </dd>
                        </div>

                        <!-- Automation & AI -->
                        <div class="flex flex-col">
                            <dt class="flex items-center gap-x-3 text-base font-semibold leading-7">
                                <div class="h-10 w-10 flex items-center justify-center rounded-lg bg-primary-100">
                                    <span class="text-primary text-lg">🤖</span>
                                </div>
                                <span>Automation & AI Integration</span>
                            </dt>
                            <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-500">
                                <p class="flex-auto">Identify opportunities for AI and automation to drive efficiency, reduce costs, and create new capabilities.</p>
                            </dd>
                        </div>

                        <!-- Technical Due Diligence -->
                        <div class="flex flex-col">
                            <dt class="flex items-center gap-x-3 text-base font-semibold leading-7">
                                <div class="h-10 w-10 flex items-center justify-center rounded-lg bg-primary-100">
                                    <span class="text-primary text-lg">🔍</span>
                                </div>
                                <span>Technical Due Diligence</span>
                            </dt>
                            <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-500">
                                <p class="flex-auto">Evaluate technology assets, architecture, and teams to identify risks and opportunities during acquisitions or investments.</p>
                            </dd>
                        </div>

                        <!-- Vendor & Tool Selection -->
                        <div class="flex flex-col">
                            <dt class="flex items-center gap-x-3 text-base font-semibold leading-7">
                                <div class="h-10 w-10 flex items-center justify-center rounded-lg bg-primary-100">
                                    <span class="text-primary text-lg">⚙️</span>
                                </div>
                                <span>Vendor & Tool Selection</span>
                            </dt>
                            <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-500">
                                <p class="flex-auto">Make informed decisions about technology vendors and tools with unbiased analysis and recommendations.</p>
                            </dd>
                        </div>

                        <!-- Data Strategy -->
                        <div class="flex flex-col">
                            <dt class="flex items-center gap-x-3 text-base font-semibold leading-7">
                                <div class="h-10 w-10 flex items-center justify-center rounded-lg bg-primary-100">
                                    <span class="text-primary text-lg">📈</span>
                                </div>
                                <span>Data Strategy & API Design</span>
                            </dt>
                            <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-500">
                                <p class="flex-auto">Develop comprehensive data strategies and API architectures that enable seamless integration and unlock business value.</p>
                            </dd>
                        </div>

                        <!-- Security & Compliance -->
                        <div class="flex flex-col">
                            <dt class="flex items-center gap-x-3 text-base font-semibold leading-7">
                                <div class="h-10 w-10 flex items-center justify-center rounded-lg bg-primary-100">
                                    <span class="text-primary text-lg">🔒</span>
                                </div>
                                <span>Security & Compliance Review</span>
                            </dt>
                            <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-500">
                                <p class="flex-auto">Identify security vulnerabilities and compliance gaps in your systems and processes with actionable recommendations.</p>
                            </dd>
                        </div>

                        <!-- Custom Advisory -->
                        <div class="flex flex-col">
                            <dt class="flex items-center gap-x-3 text-base font-semibold leading-7">
                                <div class="h-10 w-10 flex items-center justify-center rounded-lg bg-primary-100">
                                    <span class="text-primary text-lg">💡</span>
                                </div>
                                <span>Custom Advisory Services</span>
                            </dt>
                            <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-500">
                                <p class="flex-auto">Whether you need strategic guidance or hands-on advisory, we tailor our services to meet your specific needs.</p>
                            </dd>
                        </div>
                    </dl>
                </div>
            </div>
        </section>
        
        <!-- Pricing Section -->
        <section class="py-24 bg-white" id="pricing" style="background-color: var(--background-color);">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="mx-auto max-w-2xl lg:text-center mb-16">
                    <h2 class="mt-2 text-3xl font-bold tracking-tight sm:text-4xl">💰 Pricing Options</h2>
                    <p class="mt-6 text-lg leading-8 text-gray-400">We offer flexible engagement models tailored to your needs.</p>
                </div>

                <div class="mx-auto max-w-7xl">
                    <!-- Hourly Pricing -->
                    <div class="mb-16">
                        <h3 class="text-2xl font-bold mb-6 flex items-center"><span class="text-2xl mr-3">⏱</span> Hourly Consulting</h3>
                        <p class="text-lg mb-8 text-gray-500">Perfect for ad-hoc consultations, architectural guidance, code or infrastructure reviews.</p>
                        
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                            <!-- Standard Rate -->
                            <div class="pricing-card p-6">
                                <div class="pricing-card-content">
                                    <h4 class="text-xl font-semibold mb-4">Standard Rate</h4>
                                    <p class="text-3xl font-bold mb-4">$120<span class="text-lg font-normal text-gray-500">/hour</span></p>
                                    <ul class="space-y-3 mb-6">
                                        <li class="flex items-start">
                                            <svg class="h-6 w-6 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                            General technical consulting
                                        </li>
                                        <li class="flex items-start">
                                            <svg class="h-6 w-6 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                            Code reviews
                                        </li>
                                        <li class="flex items-start">
                                            <svg class="h-6 w-6 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                            Architecture guidance
                                        </li>
                                        <li class="flex items-start">
                                            <svg class="h-6 w-6 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                            Pre-call preparation
                                        </li>
                                        <li class="flex items-start">
                                            <svg class="h-6 w-6 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                            Follow-up documentation
                                        </li>
                                    </ul>
                                </div>
                                <div class="pricing-card-footer">
                                    <a href="contact.html" class="button button-bg w-full text-center">Get Started</a>
                                </div>
                            </div>

                            <!-- Startup Package -->
                            <div class="pricing-card p-6 featured-card">
                                <div class="featured-tag">
                                    <span class="bg-blue-600 text-white px-4 py-1 rounded-full text-sm font-bold inline-block shadow-md">Most Popular</span>
                                </div>
                                <div class="pricing-card-content">
                                    <div class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm inline-block mb-4">Best for Startups</div>
                                    <h4 class="text-xl font-semibold mb-4">Startup Package</h4>
                                    <p class="text-3xl font-bold mb-4">$90<span class="text-lg font-normal text-gray-500">/hour</span></p>
                                    <p class="text-sm text-gray-500 mb-4">For startups under 2 years old, < 10 employees</p>
                                    <ul class="space-y-3 mb-6">
                                        <li class="flex items-start">
                                            <svg class="h-6 w-6 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                            Everything in Standard
                                        </li>
                                        <li class="flex items-start">
                                            <svg class="h-6 w-6 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                            Startup-focused advice
                                        </li>
                                        <li class="flex items-start">
                                            <svg class="h-6 w-6 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                            MVP scoping help
                                        </li>
                                        <li class="flex items-start">
                                            <svg class="h-6 w-6 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                            Priority support
                                        </li>
                                    </ul>
                                </div>
                                <div class="pricing-card-footer">
                                    <a href="contact.html" class="button button-bg w-full text-center">Get Started</a>
                                </div>
                            </div>

                            <!-- Executive Advisory -->
                            <div class="pricing-card p-6">
                                <div class="pricing-card-content">
                                    <div class="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm inline-block mb-4">Executive Level</div>
                                    <h4 class="text-xl font-semibold mb-4">Executive Advisory</h4>
                                    <p class="text-3xl font-bold mb-4">$150<span class="text-lg font-normal text-gray-500">/hour</span></p>
                                    <p class="text-sm text-gray-500 mb-4">CTO/CIO-level support</p>
                                    <ul class="space-y-3 mb-6">
                                        <li class="flex items-start">
                                            <svg class="h-6 w-6 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                            Everything in Standard
                                        </li>
                                        <li class="flex items-start">
                                            <svg class="h-6 w-6 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                            Strategic technology planning
                                        </li>
                                        <li class="flex items-start">
                                            <svg class="h-6 w-6 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                            Board/investor presentations
                                        </li>
                                        <li class="flex items-start">
                                            <svg class="h-6 w-6 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                            Executive team coaching
                                        </li>
                                    </ul>
                                </div>
                                <div class="pricing-card-footer">
                                    <a href="contact.html" class="button button-bg w-full text-center">Get Started</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Deliverable-Based Pricing -->
                    <div>
                        <h3 class="text-2xl font-bold mb-6 flex items-center"><span class="text-2xl mr-3">📦</span> Deliverable-Based Consulting</h3>
                        <p class="text-lg mb-8 text-gray-500">Ideal for well-scoped tasks with clear outcomes.</p>
                        
                        <div class="overflow-x-auto">
                            <table class="pricing-table">
                                <thead>
                                    <tr>
                                        <th>Deliverable</th>
                                        <th>Timeline</th>
                                        <th>Price</th>
                                        <th></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>
                                            <div class="font-semibold">Technology Audit & Recommendations</div>
                                            <div class="text-sm text-gray-500">Comprehensive assessment of your current tech stack with actionable recommendations.</div>
                                        </td>
                                        <td>5–7 days</td>
                                        <td>$1,200</td>
                                        <td><a href="contact.html" class="text-blue-500 hover:underline">Inquire</a></td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="font-semibold">MVP Technical Scope & Architecture</div>
                                            <div class="text-sm text-gray-500">Detailed technical specifications for your minimum viable product.</div>
                                        </td>
                                        <td>7–10 days</td>
                                        <td>$1,800</td>
                                        <td><a href="contact.html" class="text-blue-500 hover:underline">Inquire</a></td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="font-semibold">Cloud Cost Optimization Report</div>
                                            <div class="text-sm text-gray-500">Analysis of your cloud spending with detailed optimization recommendations.</div>
                                        </td>
                                        <td>4–5 days</td>
                                        <td>$900</td>
                                        <td><a href="contact.html" class="text-blue-500 hover:underline">Inquire</a></td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="font-semibold">API Strategy & Design Blueprint</div>
                                            <div class="text-sm text-gray-500">Comprehensive API architecture and implementation plan.</div>
                                        </td>
                                        <td>5–7 days</td>
                                        <td>$1,500</td>
                                        <td><a href="contact.html" class="text-blue-500 hover:underline">Inquire</a></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="mt-8 text-center">
                            <p class="text-lg mb-4">Need something custom? Let's scope it together.</p>
                            <a href="contact.html" class="button button-bg-outline">Contact Us for Custom Pricing</a>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- Engagement Process Section -->
        <section class="py-24 bg-gray-50" id="process" style="background-color: var(--background-color-light);">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="mx-auto max-w-2xl lg:text-center mb-16">
                    <h2 class="mt-2 text-3xl font-bold tracking-tight sm:text-4xl">🔄 Standard Operating Procedure (SOP)</h2>
                    <p class="mt-6 text-lg leading-8 text-gray-400">Our consulting engagements follow a consistent, transparent process.</p>
                </div>

                <div class="max-w-4xl mx-auto">
                    <div class="space-y-12">
                        <!-- Step 1 -->
                        <div class="process-step" data-step="1">
                            <h3 class="text-xl font-semibold mb-3">Initial Discovery Call (Free, 30 mins)</h3>
                            <p class="text-gray-500 mb-4">Understand goals, assess readiness, and define engagement scope.</p>
                            <div class="rounded-lg p-6 shadow-sm border border-gray-100 process-step-card">
                                <h4 class="font-medium mb-2">What to expect:</h4>
                                <ul class="space-y-2 text-gray-500">
                                    <li class="flex items-start">
                                        <svg class="h-5 w-5 text-blue-500 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"></path>
                                        </svg>
                                        We'll discuss your business objectives and technical challenges
                                    </li>
                                    <li class="flex items-start">
                                        <svg class="h-5 w-5 text-blue-500 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"></path>
                                        </svg>
                                        Initial assessment of your current technology landscape
                                    </li>
                                    <li class="flex items-start">
                                        <svg class="h-5 w-5 text-blue-500 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"></path>
                                        </svg>
                                        Determine if we're the right fit for your needs
                                    </li>
                                </ul>
                            </div>
                        </div>

                        <!-- Step 2 -->
                        <div class="process-step" data-step="2">
                            <h3 class="text-xl font-semibold mb-3">Proposal & Scope Definition</h3>
                            <p class="text-gray-500 mb-4">A written summary with deliverables, timelines, and pricing options.</p>
                            <div class="process-step-card rounded-lg p-6 shadow-sm border border-gray-100">
                                <h4 class="font-medium mb-2">What you'll receive:</h4>
                                <ul class="space-y-2 text-gray-500">
                                    <li class="flex items-start">
                                        <svg class="h-5 w-5 text-blue-500 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"></path>
                                        </svg>
                                        Detailed scope document outlining deliverables and success criteria
                                    </li>
                                    <li class="flex items-start">
                                        <svg class="h-5 w-5 text-blue-500 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"></path>
                                        </svg>
                                        Clear timeline with milestones and check-in points
                                    </li>
                                    <li class="flex items-start">
                                        <svg class="h-5 w-5 text-blue-500 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"></path>
                                        </svg>
                                        Transparent pricing with payment terms
                                    </li>
                                </ul>
                            </div>
                        </div>

                        <!-- Step 3 -->
                        <div class="process-step" data-step="3">
                            <h3 class="text-xl font-semibold mb-3">Engagement Kickoff</h3>
                            <p class="text-gray-500 mb-4">Access to materials, meetings scheduled, Slack/email set up.</p>
                            <div class="process-step-card rounded-lg p-6 shadow-sm border border-gray-100">
                                <h4 class="font-medium mb-2">Getting started:</h4>
                                <ul class="space-y-2 text-gray-500">
                                    <li class="flex items-start">
                                        <svg class="h-5 w-5 text-blue-500 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"></path>
                                        </svg>
                                        Kickoff meeting with all stakeholders to align on objectives
                                    </li>
                                    <li class="flex items-start">
                                        <svg class="h-5 w-5 text-blue-500 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"></path>
                                        </svg>
                                        Setup of collaboration tools and communication channels
                                    </li>
                                    <li class="flex items-start">
                                        <svg class="h-5 w-5 text-blue-500 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"></path>
                                        </svg>
                                        Initial information gathering and access to relevant systems
                                    </li>
                                </ul>
                            </div>
                        </div>

                        <!-- Step 4 -->
                        <div class="process-step" data-step="4">
                            <h3 class="text-xl font-semibold mb-3">Execution & Iterations</h3>
                            <p class="text-gray-500 mb-4">We work in short iterations with regular check-ins and progress updates.</p>
                            <div class="process-step-card rounded-lg p-6 shadow-sm border border-gray-100">
                                <h4 class="font-medium mb-2">During the engagement:</h4>
                                <ul class="space-y-2 text-gray-500">
                                    <li class="flex items-start">
                                        <svg class="h-5 w-5 text-blue-500 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"></path>
                                        </svg>
                                        Regular progress updates via your preferred channel
                                    </li>
                                    <li class="flex items-start">
                                        <svg class="h-5 w-5 text-blue-500 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"></path>
                                        </svg>
                                        Iterative delivery with feedback incorporation
                                    </li>
                                    <li class="flex items-start">
                                        <svg class="h-5 w-5 text-blue-500 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"></path>
                                        </svg>
                                        Transparent communication about challenges and progress
                                    </li>
                                </ul>
                            </div>
                        </div>

                        <!-- Step 5 -->
                        <div class="process-step" data-step="5">
                            <h3 class="text-xl font-semibold mb-3">Delivery & Recommendations</h3>
                            <p class="text-gray-500 mb-4">Final documents, code, or strategic presentations are delivered.</p>
                            <div class="process-step-card rounded-lg p-6 shadow-sm border border-gray-100">
                                <h4 class="font-medium mb-2">What you'll get:</h4>
                                <ul class="space-y-2 text-gray-500">
                                    <li class="flex items-start">
                                        <svg class="h-5 w-5 text-blue-500 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"></path>
                                        </svg>
                                        Final deliverables as specified in the scope document
                                    </li>
                                    <li class="flex items-start">
                                        <svg class="h-5 w-5 text-blue-500 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"></path>
                                        </svg>
                                        Presentation of findings and recommendations
                                    </li>
                                    <li class="flex items-start">
                                        <svg class="h-5 w-5 text-blue-500 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"></path>
                                        </svg>
                                        Clear action items and next steps
                                    </li>
                                </ul>
                            </div>
                        </div>

                        <!-- Step 6 -->
                        <div class="process-step" data-step="6">
                            <h3 class="text-xl font-semibold mb-3">Post-Engagement Support (Optional)</h3>
                            <p class="text-gray-500 mb-4">Ongoing advisory or handover support billed hourly.</p>
                            <div class="process-step-card rounded-lg p-6 shadow-sm border border-gray-100">
                                <h4 class="font-medium mb-2">Continued partnership:</h4>
                                <ul class="space-y-2 text-gray-500">
                                    <li class="flex items-start">
                                        <svg class="h-5 w-5 text-blue-500 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"></path>
                                        </svg>
                                        Knowledge transfer sessions to your team
                                    </li>
                                    <li class="flex items-start">
                                        <svg class="h-5 w-5 text-blue-500 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"></path>
                                        </svg>
                                        Implementation support as needed
                                    </li>
                                    <li class="flex items-start">
                                        <svg class="h-5 w-5 text-blue-500 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"></path>
                                        </svg>
                                        Ongoing advisory relationship option
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- Why Choose Us Section -->
        <section class="py-24" id="why-us" style="background-color: var(--background-color);">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="mx-auto max-w-2xl lg:text-center mb-16">
                    <h2 class="mt-2 text-3xl font-bold tracking-tight sm:text-4xl">💼 Why Choose Us?</h2>
                    <p class="mt-6 text-lg leading-8 text-gray-400">We don't just advise — we design for action.</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10 max-w-7xl mx-auto">
                    <!-- Advantage 1 -->
                    <div class="bg-gray-50 rounded-xl p-8 shadow-sm" style="background-color: var(--background-color-light);">
                        <div class="mb-4 text-blue-500">
                            <svg class="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-3">Vendor-Neutral & Business-Centric</h3>
                        <p class="text-gray-500">We provide unbiased recommendations based on your specific needs, not vendor partnerships. Our advice is always aligned with your business objectives.</p>
                    </div>

                    <!-- Advantage 2 -->
                    <div class="bg-gray-50 rounded-xl p-8 shadow-sm" style="background-color: var(--background-color-light);">
                        <div class="mb-4 text-blue-500">
                            <svg class="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-3">Real-World Startup & Enterprise Experience</h3>
                        <p class="text-gray-500">Our team brings hands-on experience from both fast-growing startups and established enterprises, giving you balanced perspectives that work in the real world.</p>
                    </div>

                    <!-- Advantage 3 -->
                    <div class="bg-gray-50 rounded-xl p-8 shadow-sm" style="background-color: var(--background-color-light);">
                        <div class="mb-4 text-blue-500">
                            <svg class="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-3">Documentation-First Approach</h3>
                        <p class="text-gray-500">We believe in leaving you with comprehensive documentation that empowers your team long after our engagement ends, ensuring knowledge transfer and continuity.</p>
                    </div>

                    <!-- Advantage 4 -->
                    <div class="bg-gray-50 rounded-xl p-8 shadow-sm" style="background-color: var(--background-color-light);">
                        <div class="mb-4 text-blue-500">
                            <svg class="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-3">Hands-On, Not Just Strategy</h3>
                        <p class="text-gray-500">We complement strategic guidance with practical implementation support, ensuring you don't just get theories but working solutions that deliver real value.</p>
                    </div>

                    <!-- Advantage 5 -->
                    <div class="bg-gray-50 rounded-xl p-8 shadow-sm" style="background-color: var(--background-color-light);">
                        <div class="mb-4 text-blue-500">
                            <svg class="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-3">Built for Delegation & Repeatability</h3>
                        <p class="text-gray-500">Our solutions are designed to be easily delegated within your team with clear processes, documentation, and training to ensure sustainable implementation.</p>
                    </div>

                    <!-- Advantage 6 -->
                    <div class="bg-gray-50 rounded-xl p-8 shadow-sm" style="background-color: var(--background-color-light);">
                        <div class="mb-4 text-blue-500">
                            <svg class="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-3">Rapid Time-to-Value</h3>
                        <p class="text-gray-500">Our structured approach and experience enable us to deliver meaningful results quickly, often starting with small wins that build momentum toward larger transformations.</p>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- CTA Section -->
        <section class="py-24 bg-gradient-to-r from-blue-600 to-indigo-700 text-white" id="cta">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="max-w-4xl mx-auto text-center">
                    <h2 class="text-3xl sm:text-4xl font-bold mb-6 text-white" >📞 Let's Talk</h2>
                    <p class="text-xl mb-10 opacity-90 text-white">Whether you need one-off guidance or a long-term advisor, we're ready to support your next milestone.</p>
                    
                    <div class="flex flex-col sm:flex-row justify-center gap-6">
                        <a href="contact.html" class="bg-white text-blue-700 hover:bg-blue-50 px-8 py-3 rounded-full font-medium text-lg shadow-lg transition-all">
                            Book a Free Discovery Call
                        </a>
                        <a href="mailto:<EMAIL>" class="bg-transparent border-2 border-white text-white hover:bg-white/10 px-8 py-3 rounded-full font-medium text-lg transition-all">
                            Email Us Directly
                        </a>
                    </div>
                </div>
            </div>
        </section>

 <!-- f -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <!-- Company Info Column -->
                <div class="footer-info">
                    <a href="../index.html" class="footer-logo">
                        <img src="../assets/images/Negative Horizontal Version.svg" alt="Sahla Smart Solutions" class="logo-img" id="foot-logo">
                    </a>
                    <p class="footer-description">Empowering businesses through innovative venture building and automation solutions in the MENA region.</p>
                    <div class="footer-social">
                        <a href="https://eg.linkedin.com/company/sahla-solutions" target="_blank" rel="noopener noreferrer"><i class="ri-linkedin-fill"></i></a>
                        <a href="https://x.com/sahlasolutions" target="_blank" rel="noopener noreferrer"><i class="ri-twitter-fill"></i></a>
                        <a href="https://www.facebook.com/SahlaSmartSolutions/" target="_blank" rel="noopener noreferrer"><i class="ri-facebook-fill"></i></a>
                        <a href="https://www.instagram.com/sahlasolutions/" target="_blank" rel="noopener noreferrer"><i class="ri-instagram-fill"></i></a>
                    </div>
                </div>

                <!-- Quick Links Column -->
                <div class="footer-links-column">
                    <h4>Quick Links</h4>
                    <ul class="footer-links-list">
                        <li><a href="../index.html">Home</a></li>
                        <li><a href="./about.html">About Us</a></li>
                        <li><a href="./ventures.html">Our Ventures</a></li>
                        <li><a href="./partners.html">Partners</a></li>
                        <li><a href="./careers.html">Careers</a></li>
                        <li><a href="./contact.html">Contact Us</a></li>
                    </ul>
                </div>

                <!-- Services Column -->
                <div class="footer-links-column">
                    <h4>Services</h4>
                    <ul class="footer-links-list">
                        <li><a href="./process-automation.html">Process Automation</a></li>
                        <li><a href="./technology-consulting.html">Technology Consulting</a></li>
                    </ul>
                </div>

                <!-- Newsletter Column -->
                <div class="footer-newsletter">
                    <h4>Newsletter</h4>

                    <form class="mb-4">
                        <div class="flex">
                            <!-- <input type="email" placeholder="Your email" class="w-full px-4 py-2 bg-gray-800 border-none text-white text-sm rounded-l"> -->
                            <a href="javascript:void(0)" class="ml-onclick-form" onclick="ml('show', 'gturk0', true)" style="
                                display: inline-block;
                                padding: 8px 16px;
                                background-color: var(--primary-color-1);
                                color: white;
                                font-size: 16px;
                                font-family: sans-serif;
                                border: none;
                                border-radius: 6px;
                                text-decoration: none;
                                cursor: pointer;
                                transition: background-color 0.3s ease;
                                " onmouseover="this.style.backgroundColor='var(--primary-color-2)'" onmouseout="this.style.backgroundColor= 'var(--primary-color-3)'">
                                Subscribe to Newsletter
                                </a>
                        </div>
                    </form>
                    <p>Subscribe to our newsletter to stay updated with our latest news and insights.</p>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-12 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <p class="text-gray-400 text-sm mb-4 md:mb-0">© 2025 Sahla Smart Solutions. All rights reserved.</p>
                    <div class="flex space-x-6">
                        <a href="../pages/privacy-policy.html" class="text-gray-400 hover:text-white text-sm">Privacy Policy</a>
                        <a href="../pages/terms-of-service.html" class="text-gray-400 hover:text-white text-sm">Terms of Service</a>
                        <a href="../pages/cookies-policy.html" class="text-gray-400 hover:text-white text-sm">Cookies Policy</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>
    </main>

    <script>
        // Theme switch functionality
        const themeSwitch = document.getElementById('theme-switch');
        const html = document.documentElement;
        const navLogo = document.getElementById('nav-logo');
        const footLogo = document.getElementById('foot-logo');

        // Function to set and save theme
        const setTheme = (theme) => {
            html.setAttribute('data-theme', theme);
            localStorage.setItem('theme', theme);
            themeSwitch.classList.toggle('dark', theme === 'dark');

            // Update logo based on theme
            if (theme === 'dark') {
                navLogo.src = '../assets/images/Dark Horizontal Version.svg';
                footLogo.src = '../assets/images/Dark Horizontal Version.svg';
            } else {
                navLogo.src = '../assets/images/Main Horizontal Version.svg';
                footLogo.src = '../assets/images/Main Horizontal Version.svg';
            }
        };

        // Mobile menu toggle
        const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
        const navLinks = document.querySelector('.nav-links');

        mobileMenuToggle.addEventListener('change', function() {
            if (this.checked) {
                navLinks.style.display = 'flex';
            } else {
                navLinks.style.display = '';
            }
        });

        // Set the correct theme class on icon container and logo after load
        document.addEventListener('DOMContentLoaded', () => {
            const currentTheme = html.getAttribute('data-theme');
            themeSwitch.classList.toggle('dark', currentTheme === 'dark');

            // Set initial logo based on theme
            if (currentTheme === 'dark') {
                navLogo.src = '../assets/images/Dark Horizontal Version.svg';
                footLogo.src = '../assets/images/Dark Horizontal Version.svg';
            } else {
                navLogo.src = '../assets/images/Main Horizontal Version.svg';
                footLogo.src = '../assets/images/Main Horizontal Version.svg';
            }

            // Service selector functionality
            const serviceSelectors = document.querySelectorAll('.service-selector');

            serviceSelectors.forEach(selector => {
                selector.addEventListener('click', () => {
                    // Remove active class from all selectors
                    serviceSelectors.forEach(s => s.classList.remove('active'));

                    // Add active class to clicked selector
                    selector.classList.add('active');

                    // Get the service type
                    const service = selector.getAttribute('data-service');

                    // Hide all images (desktop)
                    document.querySelectorAll('.service-image').forEach(img => {
                        img.style.display = 'none';
                    });

                    // Show the selected service image (desktop)
                    const desktopImage = document.getElementById(`${service}-image`);
                    if (desktopImage) desktopImage.style.display = 'block';

                    // Show the selected service image (mobile)
                    const mobileImage = document.getElementById(`${service}-image-mobile`);
                    if (mobileImage) mobileImage.style.display = 'block';
                });
            });
        });

        // Toggle theme on click
        themeSwitch.addEventListener('click', () => {
            const isCurrentlyDark = html.getAttribute('data-theme') === 'dark';
            const newTheme = isCurrentlyDark ? 'light' : 'dark';
            setTheme(newTheme);
        });
    </script>
</body>
</html> 