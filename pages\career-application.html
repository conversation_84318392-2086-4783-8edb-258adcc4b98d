<!DOCTYPE html>
<html lang="en">
<head>     <link rel="icon" type="image/x-icon" href="../assets/images/favicon.ico?v=2">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Career Application - Sahla Smart Solutions</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>tailwind.config={theme:{extend:{colors:{primary:'#0056b3',secondary:'#4dabf7'},borderRadius:{'none':'0px','sm':'4px',DEFAULT:'8px','md':'12px','lg':'16px','xl':'20px','2xl':'24px','3xl':'32px','full':'9999px','button':'8px'}}}}</script>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/navigation.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/@emailjs/browser@3/dist/email.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/remixicon@4.5.0/fonts/remixicon.css" rel="stylesheet">
    <style>
        .application-hero {
            background-image: linear-gradient(to bottom, #111827d8, #22267a8f), url('https://images.pexels.com/photos/3184432/pexels-photo-3184432.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1&offset=');
            background-size: cover;
            background-position: center top 20%;
            padding: 8rem 0 4rem;
        }

        .job-details-card {
            background: var(--bg-light);
            border-radius: 1rem;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .requirements-list, .responsibilities-list {
            list-style-type: none;
            padding-left: 0;
        }

        .requirements-list li, .responsibilities-list li {
            position: relative;
            padding-left: 1.5rem;
            margin-bottom: 1rem;
        }

        .requirements-list li:before, .responsibilities-list li:before {
            content: "•";
            color: var(--primary-color);
            position: absolute;
            left: 0;
            font-size: 1.5rem;
        }

        .application-form {
            background: var(--bg-light);
            border-radius: 1rem;
            padding: 2rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .required-field::after {
            content: " *";
            color: #e53e3e;
        }

        .form-input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            background: var(--background-color);
            color: var(--text-color);
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        .form-input:invalid {
            border-color: #e53e3e;
        }

        .form-input:invalid:focus {
            border-color: #e53e3e;
            box-shadow: 0 0 0 1px #e53e3e;
        }

        .form-textarea {
            min-height: 150px;
            resize: vertical;
        }

        .file-upload {
            position: relative;
            display: inline-block;
        }

        .file-upload-input {
            position: absolute;
            left: 0;
            top: 0;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }

        .file-upload-label {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: var(--background-color);
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            cursor: pointer;
        }

        .error-message {
            color: #e53e3e;
            font-size: 0.875rem;
            margin-top: 0.25rem;
            display: none;
        }

        .form-input:invalid + .error-message {
            display: block;
        }
    </style>
</head>
<body class="bg-white">
    <!-- Header & Navigation -->
     <header class="header">
    <div class="container">
      <nav class="nav">
        <a href="../index.html" class="nav-logo">
          <img src="../assets/images/Main Horizontal Version.svg" alt="Sahla Logo" class="logo-img" id="nav-logo">
        </a>
        <input type="checkbox" id="mobile-menu-toggle" class="mobile-menu-toggle">
        <label for="mobile-menu-toggle" class="mobile-menu-button">
          <span></span>
          <span></span>
          <span></span>
        </label>
        <div class="nav-links">
          <div class="dropdown">
            <a href="#services" class="nav-link">Services</a>
            <div class="dropdown-content">
              <a href="./process-automation.html" class="dropdown-link">Process Automation</a>
              <a href="./technology-consulting.html" class="dropdown-link">Technology Consulting</a>
            </div>
          </div>
          <a href="./ventures.html" class="nav-link">Our Ventures</a>
          <a href="./partners.html" class="nav-link">Partners</a>
          <a href="./careers.html" class="nav-link">Careers</a>
          <a href="./blog.html" class="nav-link">Blog</a>
          <a href="./about.html" class="nav-link">About us</a>
          <div class="px-4">
            <div class="h-6 border-l-2 border-gray-300"></div>
          </div>
          <a href="./contact.html" class="px-6 py-2 !rounded-button hover:bg-opacity-90 transition-all" style="background-color: var(--background-color-light); color: var(--text-color);">Contact Sales</a>
          <div class="hidden md:flex items-center gap-4">
            <div class="theme-switch" id="theme-switch">
              <i class="ri-moon-line moon-icon"></i>
              <i class="ri-sun-line sun-icon"></i>
              <script>
                (() => {
                  const savedTheme = localStorage.getItem('theme');
                  const preferred = savedTheme || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                  document.documentElement.setAttribute('data-theme', preferred);
                })();
              </script>
            </div>
          </div>
        </div>
      </nav>
    </div>
  </header>

    <!-- Application Hero Section -->
    <section class="application-hero">
        <div class="container">
            <div class="text-center text-white">
                <h1 class="h1 mb-4" id="job-title" style="margin-top: 10%;">Loading...</h1>
                <p class="text-xl max-w-2xl" id="job-department" style="text-align: start; color: #e5e7eb;">Loading...</p>
            </div>
        </div>
    </section>

    <!-- Job Details and Application Form Section -->
    <section class="section" style="background-color: var(--background-color-light);">
        <div class="container">
                        <div class="flex justify-center">
                <p class="text-gray-400 text-sm mb-4 md:mb-0">Kindly note that all responsibilities, requirements, etc displayed are indicative and may change based on the needs of the role and the business. You will be provided a more detailed job description upon selection of a role and a meeting with the hiring manager.</p>
            </div>
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Application Form -->
                <div class="lg:col-span-2">
                    <div class="application-form">
                        <h2 class="h2 mb-6">Apply Now</h2>
                        <form id="application-form" class="space-y-6" onsubmit="return handleSubmit(event)">
                            <div class="form-group">
                                <label class="form-label required-field" for="full-name">Full Name</label>
                                <input type="text" id="full-name" name="full-name" class="form-input" required 
                                    placeholder="Enter your full name" pattern="[A-Za-z\s]{2,50}" 
                                    title="Please enter a valid name (2-50 characters, letters and spaces only)">
                                <div class="error-message">Please enter your full name</div>
                            </div>

                            <div class="form-group">
                                <label class="form-label required-field" for="email">Email Address</label>
                                <input type="email" id="email" name="email" class="form-input" required 
                                    placeholder="<EMAIL>">
                                <div class="error-message">Please enter a valid email address</div>
                            </div>

                            <div class="form-group">
                                <label class="form-label required-field" for="phone">Phone Number</label>
                                <input type="tel" id="phone" name="phone" class="form-input" required 
                                    placeholder="+20 ************" pattern="[\+\d\s\-\(\)]{10,20}"
                                    title="Please enter a valid phone number">
                                <div class="error-message">Please enter a valid phone number</div>
                            </div>

                            <div class="form-group">
                                <label class="form-label" for="linkedin">LinkedIn Profile (Optional)</label>
                                <input type="url" id="linkedin" name="linkedin" class="form-input" 
                                    placeholder="https://www.linkedin.com/in/yourprofile">
                            </div>

                            <div class="form-group">
                                <label class="form-label" for="portfolio">Portfolio/Website (Optional)</label>
                                <input type="url" id="portfolio" name="portfolio" class="form-input" 
                                    placeholder="https://yourportfolio.com">
                            </div>

                            <div class="form-group">
                                <label class="form-label required-field" for="cover-letter">Cover Letter</label>
                                <textarea id="cover-letter" name="cover-letter" class="form-input form-textarea" required 
                                    placeholder="Tell us about yourself and why you're interested in this position..."></textarea>
                                <div class="error-message">Please provide a cover letter</div>
                            </div>

                            <div class="form-group">
                                <label class="form-label required-field">Resume/CV</label>
                                <div class="file-upload">
                                    <input type="file" id="resume" name="resume" class="file-upload-input" accept=".pdf,.doc,.docx,.txt" required>
                                    <label for="resume" class="file-upload-label">
                                        <i class="ri-upload-2-line mr-2"></i>
                                        Choose File
                                    </label>
                                </div>
                                <p class="text-sm text-gray-500 mt-2">Accepted formats: PDF, DOC, DOCX (Max 5MB)</p>
                                <div class="error-message">Please upload your resume</div>
                            </div>

                            <button type="submit" class="btn btn-primary w-full" id="form-submit-button">Submit Application</button>
                        </form>
                    </div>
                </div>
                                <!-- Job Details -->
                <div class="lg:col-span-1">
                    <div class="job-details-card">
                        <h2 class="h2 mb-6">Job Description</h2>
                        <div id="job-description" class="mb-8">
                            Loading...
                        </div>

                        <h3 class="h3 mb-4">Requirements</h3>
                        <ul class="requirements-list mb-8" id="job-requirements">
                            <li>Loading...</li>
                        </ul>

                        <h3 class="h3 mb-4">Responsibilities</h3>
                        <ul class="responsibilities-list" id="job-responsibilities">
                            <li>Loading...</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- f -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <!-- Company Info Column -->
                <div class="footer-info">
                    <a href="../index.html" class="footer-logo">
                        <img src="../assets/images/Negative Horizontal Version.svg" alt="Sahla Smart Solutions" class="logo-img" id="foot-logo">
                    </a>
                    <p class="footer-description">Empowering businesses through innovative venture building and automation solutions in the MENA region.</p>
                    <div class="footer-social">
                        <a href="https://eg.linkedin.com/company/sahla-solutions" target="_blank" rel="noopener noreferrer"><i class="ri-linkedin-fill"></i></a>
                        <a href="https://x.com/sahlasolutions" target="_blank" rel="noopener noreferrer"><i class="ri-twitter-fill"></i></a>
                        <a href="https://www.facebook.com/SahlaSmartSolutions/" target="_blank" rel="noopener noreferrer"><i class="ri-facebook-fill"></i></a>
                        <a href="https://www.instagram.com/sahlasolutions/" target="_blank" rel="noopener noreferrer"><i class="ri-instagram-fill"></i></a>
                    </div>
                </div>

                <!-- Quick Links Column -->
                <div class="footer-links-column">
                    <h4>Quick Links</h4>
                    <ul class="footer-links-list">
                        <li><a href="../index.html">Home</a></li>
                        <li><a href="./about.html">About Us</a></li>
                        <li><a href="./ventures.html">Our Ventures</a></li>
                        <li><a href="./partners.html">Partners</a></li>
                        <li><a href="./careers.html">Careers</a></li>
                        <li><a href="./contact.html">Contact Us</a></li>
                    </ul>
                </div>

                <!-- Services Column -->
                <div class="footer-links-column">
                    <h4>Services</h4>
                    <ul class="footer-links-list">
                        <li><a href="./process-automation.html">Process Automation</a></li>
                        <li><a href="./technology-consulting.html">Technology Consulting</a></li>
                    </ul>
                </div>

                <!-- Newsletter Column -->
                <div class="footer-newsletter">
                    <h4>Newsletter</h4>

                    <form class="mb-4">
                        <div class="flex">
                            <!-- <input type="email" placeholder="Your email" class="w-full px-4 py-2 bg-gray-800 border-none text-white text-sm rounded-l"> -->
                            <a href="javascript:void(0)" class="ml-onclick-form" onclick="ml('show', 'gturk0', true)" style="
                                display: inline-block;
                                padding: 8px 16px;
                                background-color: var(--primary-color-1);
                                color: white;
                                font-size: 16px;
                                font-family: sans-serif;
                                border: none;
                                border-radius: 6px;
                                text-decoration: none;
                                cursor: pointer;
                                transition: background-color 0.3s ease;
                                " onmouseover="this.style.backgroundColor='var(--primary-color-2)'" onmouseout="this.style.backgroundColor= 'var(--primary-color-3)'">
                                Subscribe to Newsletter
                                </a>
                        </div>
                    </form>
                    <p>Subscribe to our newsletter to stay updated with our latest news and insights.</p>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-12 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <p class="text-gray-400 text-sm mb-4 md:mb-0">© 2025 Sahla Smart Solutions. All rights reserved.</p>
                    <div class="flex space-x-6">
                        <a href="../pages/privacy-policy.html" class="text-gray-400 hover:text-white text-sm">Privacy Policy</a>
                        <a href="../pages/terms-of-service.html" class="text-gray-400 hover:text-white text-sm">Terms of Service</a>
                        <a href="../pages/cookies-policy.html" class="text-gray-400 hover:text-white text-sm">Cookies Policy</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Jobs Data -->
    <script src="../assets/js/jobs-data.js"></script>
    
    <script>
        // Theme switch functionality
        const themeSwitch = document.getElementById('theme-switch');
        const html = document.documentElement;
        const navLogo = document.getElementById('nav-logo');
        const footLogo = document.getElementById('foot-logo');

        // Function to set and save theme
        const setTheme = (theme) => {
            html.setAttribute('data-theme', theme);
            localStorage.setItem('theme', theme);
            themeSwitch.classList.toggle('dark', theme === 'dark');
            
            // Update logo based on theme
            if (theme === 'dark') {
                navLogo.src = '../assets/images/Dark Horizontal Version.svg';
                footLogo.src = '../assets/images/Dark Horizontal Version.svg';
            } else {
                navLogo.src = '../assets/images/Main Horizontal Version.svg';
                footLogo.src = '../assets/images/Main Horizontal Version.svg';
            }
        };

        // Set the correct theme class on icon container and logo after load
        document.addEventListener('DOMContentLoaded', () => {
            const currentTheme = html.getAttribute('data-theme');
            themeSwitch.classList.toggle('dark', currentTheme === 'dark');
            
            // Set initial logo based on theme
            if (currentTheme === 'dark') {
                navLogo.src = '../assets/images/Dark Horizontal Version.svg';
                footLogo.src = '../assets/images/Dark Horizontal Version.svg';
            } else {
                navLogo.src = '../assets/images/Main Horizontal Version.svg';
                footLogo.src = '../assets/images/Main Horizontal Version.svg';
            }
        });

        // Toggle theme on click
        themeSwitch.addEventListener('click', () => {
            const isCurrentlyDark = html.getAttribute('data-theme') === 'dark';
            const newTheme = isCurrentlyDark ? 'light' : 'dark';
            setTheme(newTheme);
        });

        // Watch for system theme changes
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
            if (!localStorage.getItem('theme')) {
                setTheme(e.matches ? 'dark' : 'light');
            }
        });

        // Mobile menu toggle
        const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
        const navLinks = document.querySelector('.nav-links');

        mobileMenuToggle.addEventListener('change', function() {
            if (this.checked) {
                navLinks.style.display = 'flex';
            } else {
                navLinks.style.display = '';
            }
        });

        // Get job ID from URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const jobId = urlParams.get('job');

        // Function to fetch jobs data
        function fetchJobsData() {
            return new Promise((resolve, reject) => {
                // Check if jobsData is already available
                if (typeof jobsData !== 'undefined') {
                    resolve(jobsData);
                    return;
                }

                // If not, try to load it from the external file
                const script = document.createElement('script');
                script.src = '../assets/js/jobs-data.js';
                script.onload = () => {
                    if (typeof jobsData !== 'undefined') {
                        resolve(jobsData);
                    } else {
                        reject(new Error('Failed to load jobs data'));
                    }
                };
                script.onerror = () => {
                    reject(new Error('Failed to load jobs data script'));
                };
                document.head.appendChild(script);
            });
        }

        // Load and display job details
        async function loadJobDetails() {
            try {
                // First fetch the jobs data
                const jobsData = await fetchJobsData();
                
                if (!jobId) {
                    window.location.href = '/pages/careers.html';
                    return;
                }

                const job = jobsData.jobs.find(j => j.id === parseInt(jobId));
                if (!job) {
                    window.location.href = '/pages/careers.html';
                    return;
                }

                const department = jobsData.departments.find(d => d.id === job.department);
                const location = jobsData.locations.find(l => l.id === job.location);

                // Update page title
                document.title = `${job.title} - Career Application - Sahla Smart Solutions`;

                // Update job details
                document.getElementById('job-title').textContent = job.title;
                document.getElementById('job-department').textContent = `${department.name} • ${location.name}`;
                document.getElementById('job-description').innerHTML = job.description;

                // Update requirements
                const requirementsList = document.getElementById('job-requirements');
                requirementsList.innerHTML = job.requirements.map(req => `<li>${req}</li>`).join('');

                // Update responsibilities
                const responsibilitiesList = document.getElementById('job-responsibilities');
                responsibilitiesList.innerHTML = job.responsibilities.map(resp => `<li>${resp}</li>`).join('');
            } catch (error) {
                console.error('Error loading job details:', error);
                document.getElementById('job-title').textContent = 'Error Loading Job';
                document.getElementById('job-department').textContent = 'Please try again later';
            }
        }

        async function handleSubmit(event) {
            event.preventDefault();
            
            // Get form data
            const formElement = event.target;
            const formData = new FormData(formElement);
            
            // Add form type to indicate this is a career application
            formData.append('form-type', 'career');
            
            // Get the submit button and store its original text
            const submitButton = document.getElementById('form-submit-button');
            const originalButtonText = submitButton.innerHTML;
            
            try {
                // Show loading state
                submitButton.innerHTML = 'Submitting...';
                submitButton.disabled = true;
                
                // Send the form data to our PHP endpoint
                const response = await fetch('/contact-api/send.php', {
                    method: 'POST',
                    body: formData
                });
                
                // Check if the response is OK before trying to parse JSON
                if (!response.ok) {
                    throw new Error(`Server responded with status: ${response.status}`);
                }
                
                // Try to parse the JSON response
                let result;
                try {
                    const text = await response.text();
                    result = JSON.parse(text);
                } catch (parseError) {
                    console.error('Error parsing server response:', parseError);
                    throw new Error('Invalid response from server');
                }
                
                if (result.success) {
                    alert('Your application was submitted successfully!');
                    window.location.href = './contact-success.html';
                } else {
                    console.error('Error submitting form:', result.message);
                    if (result.errors && result.errors.length > 0) {
                        alert('Error: ' + result.errors.join(', '));
                    } else {
                        alert('There was an error submitting your application. Please try again later.');
                    }
                }
            } catch (error) {
                console.error('Error sending application:', error);
                alert('Error submitting form. Please try again later.');
            } finally {
                // Reset button state
                submitButton.innerHTML = originalButtonText;
                submitButton.disabled = false;
            }
            
            return false;
        }

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            loadJobDetails();
        });
    </script>
</body>
<script>'undefined'=== typeof _trfq || (window._trfq = []);'undefined'=== typeof _trfd && (window._trfd=[]),_trfd.push({'tccl.baseHost':'secureserver.net'},{'ap':'cpsh-oh'},{'server':'sxb1plzcpnl453516'},{'dcenter':'sxb1'},{'cp_id':'9153305'},{'cp_cache':''},{'cp_cl':'8'}) // Monitoring performance to make your website faster. If you want to opt-out, please contact web hosting support.</script><script src='https://img1.wsimg.com/traffic-assets/js/tccl.min.js'></script></html> 