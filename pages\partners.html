<!DOCTYPE html>
<html lang="en">

<head>     <link rel="icon" type="image/x-icon" href="../assets/images/favicon.ico?v=2">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Partners - Sahla Smart Solutions</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#0056b3',
                        secondary: '#4dabf7'
                    },
                    borderRadius: {
                        'none': '0px',
                        'sm': '4px',
                        DEFAULT: '8px',
                        'md': '12px',
                        'lg': '16px',
                        'xl': '20px',
                        '2xl': '24px',
                        '3xl': '32px',
                        'full': '9999px',
                        'button': '8px'
                    }
                }
            }
        }
    </script>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/navigation.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@4.5.0/fonts/remixicon.css" rel="stylesheet">
    <script src="../assets/js/partners-data.js"></script>
    <script src="../assets/js/success-stories-data.js"></script>
    <script src="../assets/js/collaboration-areas.js"></script>
    <style>
         :where([class^="ri-"])::before {
            content: "\f3c2";
        }
        
        .partners-hero {
            background-image: linear-gradient(to bottom, #111827d8, #22267a8f), url('https://images.pexels.com/photos/3184418/pexels-photo-3184418.jpeg');
            background-size: 100% auto;
            background-position: center top 30%;
        }
        
        .process-line {
            background: linear-gradient(90deg, var(--primary-color-1) 0%, var(--secondary-color-1) 100%);
            height: 2px;
            position: absolute;
            top: 3rem;
            left: 2.5rem;
            right: 2.5rem;
            z-index: 0;
        }
        
        .process-step {
            z-index: 1;
            position: relative;
        }
        
        .process-number {
            width: 3rem;
            height: 3rem;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background-color: white;
            border: 2px solid var(--primary-color-1);
            color: var(--primary-color-1);
            font-weight: 700;
        }
        /* Partner section styles */
        

        /* Markdown content styling */
        #venture-detail-content {
        font-family: Arial, sans-serif;
        line-height: 1.6;
        color: #333;
        }

        /* Headers */
        #venture-detail-content h1 {
        font-size: 2em;
        margin-bottom: 0.5em;
        }
        #venture-detail-content h2 {
        font-size: 1.5em;
        margin-top: 1em;
        margin-bottom: 0.5em;
        }

        /* Paragraphs */
        #venture-detail-content p {
        margin: 0.5em 0;
        }

        /* Lists */
        #venture-detail-content ul {
        margin: 0.5em 0 0.5em 1.5em;
        }

        /* Bold text */
        #venture-detail-content strong {
        font-weight: bold;
        }

        #venture-detail-content img {
        display: block;
        margin: 1.5em auto;
        }

        #venture-detail-content ul {
        list-style-type: disc;     /* Ensures bullets are shown */
        margin-left: 1.5em;        /* Indent the list */
        padding-left: 1em;         /* Optional extra spacing */
        }

        #venture-detail-content li {
        margin-bottom: 0.5em;      /* Space between list items */
        }
        #venture-detail-content ol {
        list-style-type: decimal;
        margin-left: 1.5em;
        padding-left: 1em;
        }

        #venture-detail-content ul li::marker {
        color: #007bff; /* Make bullets blue */
        font-size: 1.2em;
        }

        .partners-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }
        
        .partner-logo-card {
            aspect-ratio: 1;
            width: 100%;
            max-width: 200px;
            margin: 0 auto;
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.3s ease, transform 0.3s ease;
        }
        
        .partner-logo-card.visible {
            opacity: 1;
            transform: translateY(0);
        }
        
        .partner-logo-content {
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 1.5rem;
            background: var(--bg-light);
            border-radius: 1rem;
            transition: all 0.3s ease;
        }
        
        .partner-logo-content:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }
        
        .partner-icon-wrapper {
            width: 64px;
            height: 64px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
            font-size: 2rem;
            color: var(--primary-color);
        }
        
        .pagination-btn {
            background-color: var(--background-color-light);
            color: var(--text-color);
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
            cursor: pointer;
        }
        
        .pagination-btn:hover {
            background-color: var(--background-color);
            color: var(--text-color);
        }
        
        .pagination-btn:active {
            background-color: var(--background-color);
            color: var(--text-color);
        }
        
        .pagination-number {
            background-color: var(--background-color-light);
            color: var(--text-color);
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
            cursor: pointer;
        }
        
        .pagination-number:hover {
            background-color: var(--background-color);
            color: var(--text-color);
        }
        
        .pagination-number:active {
            background-color: var(--background-color);
            color: var(--text-color);
        }
        
        .partner-name {
            text-align: center;
            font-weight: 500;
            color: var(--text-color);
        }
        
        .filter-btn {
            padding: 0.5rem 1.5rem;
            border-radius: 2rem;
            background: transparent;
            color: var(--primary-color);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .filter-btn:hover {
            background: var(--primary-color);
            color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .filter-btn.active {
            background: var(--primary-color-1);
            color: white;
            border-color: var(--primary-color);
        }
        /* Sahla Edge Styles */
        
        .sahla-edge {
            margin-top: 3rem;
        }
        
        .edge-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }
        
        .edge-item {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            padding: 1.5rem;
            background: var(--bg-light);
            border-radius: 1rem;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .edge-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }
        
        .edge-icon {
            width: 48px;
            height: 48px;
            min-width: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--primary-color);
            color: var(--primary-color-1);
            border-radius: 12px;
            font-size: 1.5rem;
        }
        
        .edge-content h4 {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--text-color);
        }
        
        .edge-content p {
            font-size: 0.9rem;
            line-height: 1.5;
            color: var(--text-light);
            margin: 0;
        }
        
        @media (max-width: 768px) {
            .edge-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
    <!-- MailerLite Universal -->
    <script>
        (function(w, d, e, u, f, l, n) {
            w[f] = w[f] || function() {
                    (w[f].q = w[f].q || [])
                    .push(arguments);
                }, l = d.createElement(e), l.async = 1, l.src = u,
                n = d.getElementsByTagName(e)[0], n.parentNode.insertBefore(l, n);
        })
        (window, document, 'script', 'https://assets.mailerlite.com/js/universal.js', 'ml');
        ml('account', '1446707');
    </script>
    <!-- End MailerLite Universal -->
</head>

<body class="bg-white">
    <!-- Header & Navigation -->
 <header class="header">
    <div class="container">
      <nav class="nav">
        <a href="../index.html" class="nav-logo">
          <img src="../assets/images/Main Horizontal Version.svg" alt="Sahla Logo" class="logo-img" id="nav-logo">
        </a>
        <input type="checkbox" id="mobile-menu-toggle" class="mobile-menu-toggle">
        <label for="mobile-menu-toggle" class="mobile-menu-button">
          <span></span>
          <span></span>
          <span></span>
        </label>
        <div class="nav-links">
          <div class="dropdown">
            <a href="#services" class="nav-link">Services</a>
            <div class="dropdown-content">
              <a href="./process-automation.html" class="dropdown-link">Process Automation</a>
              <a href="./technology-consulting.html" class="dropdown-link">Technology Consulting</a>
            </div>
          </div>
          <a href="./ventures.html" class="nav-link">Our Ventures</a>
          <a href="./partners.html" class="nav-link">Partners</a>
          <a href="./careers.html" class="nav-link">Careers</a>
          <a href="./blog.html" class="nav-link">Blog</a>
          <a href="./about.html" class="nav-link">About us</a>
          <div class="px-4">
            <div class="h-6 border-l-2 border-gray-300"></div>
          </div>
          <a href="./contact.html" class="px-6 py-2 !rounded-button hover:bg-opacity-90 transition-all" style="background-color: var(--background-color-light); color: var(--text-color);">Contact Sales</a>
          <div class="hidden md:flex items-center gap-4">
            <div class="theme-switch" id="theme-switch">
              <i class="ri-moon-line moon-icon"></i>
              <i class="ri-sun-line sun-icon"></i>
              <script>
                (() => {
                  const savedTheme = localStorage.getItem('theme');
                  const preferred = savedTheme || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                  document.documentElement.setAttribute('data-theme', preferred);
                })();
              </script>
            </div>
          </div>
        </div>
      </nav>
    </div>
  </header>

    <!-- Hero Section -->
    <section class="partners-hero pt-32 pb-16">
        <div class="container mx-auto px-4" style="margin-top: 10%;">
            <h1 class="text-4xl md:text-5xl font-bold text-align:start mb-6" style="color: white;">Partners in Success</h1>
            <p class="text-xl text-align:start max-w-2xl" style="color: white;">Together we achieve the impossible.</p>
        </div>
    </section>

    <!-- Partners Section -->
    <section id="partners" class="section bg-light">
        <div class="container">
            <div class="text-center mb-16">
                <h2 class="section-title">Our Partners</h2>
                <p class="section-subtitle">We collaborate with leading organizations across industries to build innovative ventures.</p>
            </div>
            <div class="partner-filter-container">
                <div class="partner-category-selector" id="partner-categories">
                    <!-- Categories will be populated by JavaScript -->
                </div>
            </div>

            <div class="partners-grid" id="partners-grid">
                <!-- Partner cards will be populated by JavaScript -->
            </div>

            <!-- Pagination Controls -->
            <div class="pagination-container flex justify-center items-center mt-8">
                <div class="pagination-controls flex items-center space-x-2">
                    <button id="prev-page" class="pagination-btn bg-gray-200 hover:bg-gray-300 text-gray-700 px-3 py-1 rounded-md transition-colors">
                        <i class="ri-arrow-left-s-line"></i> Previous
                    </button>
                    <div id="pagination-numbers" class="flex space-x-1">
                        <!-- Page numbers will be populated by JavaScript -->
                    </div>
                    <button id="next-page" class="pagination-btn bg-gray-200 hover:bg-gray-300 text-gray-700 px-3 py-1 rounded-md transition-colors">
                        Next <i class="ri-arrow-right-s-line"></i>
                    </button>
                </div>
            </div>

            <div class="text-center mt-6">
                <a href="./contact.html" class="btn btn-primary">Become a Partner</a>
            </div>
        </div>
    </section>

    <!-- Why Work With Us Section -->
    <section id="why-work-with-us" class="section">
        <div class="container">
            <div class="text-center mb-16">
                <h2 class="section-title">Why Work With Us?</h2>
                <p class="section-subtitle">We combine expertise, innovation, and a proven approach to deliver exceptional results.</p>
            </div>

            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="ri-robot-line --primary-color"></i>
                    </div>
                    <h3 class="h3">AI-Powered Operations</h3>
                    <p>Leverage cutting-edge artificial intelligence to automate complex processes and gain valuable insights from your data.</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="ri-line-chart-line"></i>
                    </div>
                    <h3 class="h3">Practical, Scalable Results</h3>
                    <p>Our solutions deliver immediate value while being designed to scale with your business as it grows.</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="ri-team-line"></i>
                    </div>
                    <h3 class="h3">Cross-functional Teams</h3>
                    <p>Work with diverse teams of experts across technology, business, and industry domains for comprehensive solutions.</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="ri-settings-4-line"></i>
                    </div>
                    <h3 class="h3">Agile & Flexible</h3>
                    <p>Our agile approach allows for rapid iterations and adjustments to ensure we meet your evolving needs.</p>
                </div>
            </div>
        </div>
    </section>


    <!-- Collaboration Areas Section -->
    <section id="collaboration-areas" class="section bg-light">
        <div class="container">
            <div class="text-center mb-16">
                <h2 class="section-title">Open Collaboration Areas</h2>
                <p class="section-subtitle">Explore the diverse areas where we can collaborate to create innovative solutions.</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6" id="collaboration-areas-grid">
                <!-- Collaboration area cards will be populated by JavaScript -->
            </div>
        </div>
    </section>

<!-- f -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <!-- Company Info Column -->
                <div class="footer-info">
                    <a href="../index.html" class="footer-logo">
                        <img src="../assets/images/Negative Horizontal Version.svg" alt="Sahla Smart Solutions" class="logo-img" id="foot-logo">
                    </a>
                    <p class="footer-description">Empowering businesses through innovative venture building and automation solutions in the MENA region.</p>
                    <div class="footer-social">
                        <a href="https://eg.linkedin.com/company/sahla-solutions" target="_blank" rel="noopener noreferrer"><i class="ri-linkedin-fill"></i></a>
                        <a href="https://x.com/sahlasolutions" target="_blank" rel="noopener noreferrer"><i class="ri-twitter-fill"></i></a>
                        <a href="https://www.facebook.com/SahlaSmartSolutions/" target="_blank" rel="noopener noreferrer"><i class="ri-facebook-fill"></i></a>
                        <a href="https://www.instagram.com/sahlasolutions/" target="_blank" rel="noopener noreferrer"><i class="ri-instagram-fill"></i></a>
                    </div>
                </div>

                <!-- Quick Links Column -->
                <div class="footer-links-column">
                    <h4>Quick Links</h4>
                    <ul class="footer-links-list">
                        <li><a href="../index.html">Home</a></li>
                        <li><a href="./about.html">About Us</a></li>
                        <li><a href="./ventures.html">Our Ventures</a></li>
                        <li><a href="./partners.html">Partners</a></li>
                        <li><a href="./careers.html">Careers</a></li>
                        <li><a href="./contact.html">Contact Us</a></li>
                    </ul>
                </div>

                <!-- Services Column -->
                <div class="footer-links-column">
                    <h4>Services</h4>
                    <ul class="footer-links-list">
                        <li><a href="./process-automation.html">Process Automation</a></li>
                        <li><a href="./technology-consulting.html">Technology Consulting</a></li>
                    </ul>
                </div>

                <!-- Newsletter Column -->
                <div class="footer-newsletter">
                    <h4>Newsletter</h4>

                    <form class="mb-4">
                        <div class="flex">
                            <!-- <input type="email" placeholder="Your email" class="w-full px-4 py-2 bg-gray-800 border-none text-white text-sm rounded-l"> -->
                            <a href="javascript:void(0)" class="ml-onclick-form" onclick="ml('show', 'gturk0', true)" style="
                                display: inline-block;
                                padding: 8px 16px;
                                background-color: var(--primary-color-1);
                                color: white;
                                font-size: 16px;
                                font-family: sans-serif;
                                border: none;
                                border-radius: 6px;
                                text-decoration: none;
                                cursor: pointer;
                                transition: background-color 0.3s ease;
                                " onmouseover="this.style.backgroundColor='var(--primary-color-2)'" onmouseout="this.style.backgroundColor= 'var(--primary-color-3)'">
                                Subscribe to Newsletter
                                </a>
                        </div>
                    </form>
                    <p>Subscribe to our newsletter to stay updated with our latest news and insights.</p>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-12 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <p class="text-gray-400 text-sm mb-4 md:mb-0">© 2025 Sahla Smart Solutions. All rights reserved.</p>
                    <div class="flex space-x-6">
                        <a href="../pages/privacy-policy.html" class="text-gray-400 hover:text-white text-sm">Privacy Policy</a>
                        <a href="../pages/terms-of-service.html" class="text-gray-400 hover:text-white text-sm">Terms of Service</a>
                        <a href="../pages/cookies-policy.html" class="text-gray-400 hover:text-white text-sm">Cookies Policy</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Theme switch functionality
        const themeSwitch = document.getElementById('theme-switch');
        const html = document.documentElement;
        const navLogo = document.getElementById('nav-logo');
        const footLogo = document.getElementById('foot-logo');

        // Function to set and save theme
        const setTheme = (theme) => {
            html.setAttribute('data-theme', theme);
            localStorage.setItem('theme', theme);
            themeSwitch.classList.toggle('dark', theme === 'dark');

            // Update logo based on theme
            if (theme === 'dark') {
                navLogo.src = '../assets/images/Dark Horizontal Version.svg';
                footLogo.src = '../assets/images/Dark Horizontal Version.svg';
            } else {
                navLogo.src = '../assets/images/Main Horizontal Version.svg';
                footLogo.src = '../assets/images/Main Horizontal Version.svg';
            }
        };

        // Set the correct theme class on icon container and logo after load
        document.addEventListener('DOMContentLoaded', () => {
            const currentTheme = html.getAttribute('data-theme');
            themeSwitch.classList.toggle('dark', currentTheme === 'dark');

            // Set initial logo based on theme
            if (currentTheme === 'dark') {
                navLogo.src = '../assets/images/Dark Horizontal Version.svg';
                footLogo.src = '../assets/images/Dark Horizontal Version.svg';
            } else {
                navLogo.src = '../assets/images/Main Horizontal Version.svg';
                footLogo.src = '../assets/images/Main Horizontal Version.svg';
            }
        });

        // Toggle theme on click
        themeSwitch.addEventListener('click', () => {
            const isCurrentlyDark = html.getAttribute('data-theme') === 'dark';
            const newTheme = isCurrentlyDark ? 'light' : 'dark';
            setTheme(newTheme);
        });

        // Watch for system theme changes
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
            if (!localStorage.getItem('theme')) {
                setTheme(e.matches ? 'dark' : 'light');
            }
        });

        // Mobile menu toggle
        const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
        const navLinks = document.querySelector('.nav-links');

        mobileMenuToggle.addEventListener('change', function() {
            if (this.checked) {
                navLinks.style.display = 'flex';
            } else {
                navLinks.style.display = '';
            }
        });

        // Submenu toggle
        function toggleSubmenu(id) {
            const submenu = document.getElementById(id);
            submenu.classList.toggle('hidden');
        }

        // Custom checkbox toggle
        function toggleCheckbox(checkbox) {
            checkbox.classList.toggle('checked');
        }

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function(e) {
                e.preventDefault();

                const targetId = this.getAttribute('href');
                if (targetId === '#') return;

                const targetElement = document.querySelector(targetId);
                if (targetElement) {
                    const headerHeight = 80; // Approximate header height
                    const targetPosition = targetElement.getBoundingClientRect().top + window.pageYOffset - headerHeight;

                    window.scrollTo({
                        top: targetPosition,
                        behavior: 'smooth'
                    });

                    // Close mobile menu if open
                    if (!mobileMenuToggle.checked) {
                        mobileMenuToggle.checked = false;
                        navLinks.style.display = '';
                    }
                }
            });
        });

        // Sticky header effect
        window.addEventListener('scroll', () => {
            const header = document.querySelector('header');
            if (window.scrollY > 50) {
                header.classList.add('shadow-md');
            } else {
                header.classList.remove('shadow-md');
            }
        });

        // Success Stories Carousel functionality


        // Initialize success stories carousel when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            initializePartnersSection();
            initializeCollaborationAreas();
            // ... existing DOMContentLoaded code ...
        });

        // Partners section functionality
        function initializePartnersSection() {
            const categoriesContainer = document.getElementById('partner-categories');
            const partnersGrid = document.getElementById('partners-grid');
            const paginationNumbers = document.getElementById('pagination-numbers');
            const prevPageBtn = document.getElementById('prev-page');
            const nextPageBtn = document.getElementById('next-page');

            let currentCategory = 'all';
            let currentPage = 1;
            const partnersPerPage = 6; // Number of partners to display per page
            let filteredPartners = [];
            let totalPages = 1;

            // Populate category buttons
            partnersData.categories.forEach(category => {
                const button = document.createElement('button');
                button.className = `filter-btn ${category.id === 'all' ? 'active' : ''}`;
                button.textContent = category.name;
                button.dataset.category = category.id;
                button.addEventListener('click', () => {
                    currentPage = 1; // Reset to first page when changing category
                    filterPartners(category.id);
                });
                categoriesContainer.appendChild(button);
            });

            // Function to filter partners
            function filterPartners(category) {
                currentCategory = category;

                // Update active button
                document.querySelectorAll('.filter-btn').forEach(btn => {
                    btn.classList.toggle('active', btn.dataset.category === category);
                });

                // Filter partners
                filteredPartners = category === 'all' ?
                    partnersData.partners :
                    partnersData.partners.filter(partner => partner.category === category);

                // Calculate total pages
                totalPages = Math.ceil(filteredPartners.length / partnersPerPage);

                // Update pagination
                updatePagination();

                // Display current page
                displayPartnersPage(currentPage);
            }

            // Function to display partners for the current page
            function displayPartnersPage(page) {
                // Clear grid
                partnersGrid.innerHTML = '';

                // Calculate start and end indices for the current page
                const startIndex = (page - 1) * partnersPerPage;
                const endIndex = Math.min(startIndex + partnersPerPage, filteredPartners.length);

                // Get partners for the current page
                const partnersForPage = filteredPartners.slice(startIndex, endIndex);

                // Add new cards with animation
                partnersForPage.forEach((partner, index) => {
                    const card = document.createElement('div');
                    card.className = 'partner-logo-card';
                    card.innerHTML = `
                        <div class="partner-logo-content">
                            <div class="partner-icon-wrapper">
                                <i class="${partner.icon}"></i>
                            </div>
                            <span class="partner-name">${partner.name}</span>
                        </div>
                    `;
                    partnersGrid.appendChild(card);

                    // Trigger animation after a small delay
                    setTimeout(() => {
                        card.classList.add('visible');
                    }, index * 50); // Stagger the animations
                });

                // Update pagination buttons state
                updatePaginationButtons();
            }

            // Function to update pagination numbers
            function updatePagination() {
                paginationNumbers.innerHTML = '';

                // Determine which page numbers to show
                let startPage = Math.max(1, currentPage - 2);
                let endPage = Math.min(totalPages, startPage + 4);

                // Adjust start page if we're near the end
                if (endPage - startPage < 4) {
                    startPage = Math.max(1, endPage - 4);
                }

                // Add first page if not included
                if (startPage > 1) {
                    addPageButton(1);
                    if (startPage > 2) {
                        addEllipsis();
                    }
                }

                // Add page numbers
                for (let i = startPage; i <= endPage; i++) {
                    addPageButton(i);
                }

                // Add last page if not included
                if (endPage < totalPages) {
                    if (endPage < totalPages - 1) {
                        addEllipsis();
                    }
                    addPageButton(totalPages);
                }
            }

            // Helper function to add a page button
            function addPageButton(pageNum) {
                const button = document.createElement('button');
                button.className = `pagination-number w-8 h-8 flex items-center justify-center rounded-md ${pageNum === currentPage ? 'bg-primary text-white' : 'bg-gray-200 hover:bg-gray-300 text-gray-700'}`;
                button.textContent = pageNum;
                button.addEventListener('click', () => {
                    if (pageNum !== currentPage) {
                        currentPage = pageNum;
                        displayPartnersPage(currentPage);
                        updatePagination();
                    }
                });
                paginationNumbers.appendChild(button);
            }

            // Helper function to add ellipsis
            function addEllipsis() {
                const ellipsis = document.createElement('span');
                ellipsis.className = 'px-2 text-gray-500';
                ellipsis.textContent = '...';
                paginationNumbers.appendChild(ellipsis);
            }

            // Function to update pagination buttons state
            function updatePaginationButtons() {
                prevPageBtn.disabled = currentPage === 1;
                prevPageBtn.classList.toggle('opacity-50', currentPage === 1);
                prevPageBtn.classList.toggle('cursor-not-allowed', currentPage === 1);

                nextPageBtn.disabled = currentPage === totalPages;
                nextPageBtn.classList.toggle('opacity-50', currentPage === totalPages);
                nextPageBtn.classList.toggle('cursor-not-allowed', currentPage === totalPages);
            }

            // Add event listeners for pagination buttons
            prevPageBtn.addEventListener('click', () => {
                if (currentPage > 1) {
                    currentPage--;
                    displayPartnersPage(currentPage);
                    updatePagination();
                }
            });

            nextPageBtn.addEventListener('click', () => {
                if (currentPage < totalPages) {
                    currentPage++;
                    displayPartnersPage(currentPage);
                    updatePagination();
                }
            });

            // Initialize with all partners
            filterPartners('all');
        }

        // Collaboration Areas functionality
        function initializeCollaborationAreas() {
            const grid = document.getElementById('collaboration-areas-grid');

            collaborationAreasData.areas.forEach(area => {
                const card = document.createElement('div');
                card.className = 'p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300';
                card.style.backgroundColor = 'var(--background-color)';
                card.innerHTML = `
                    <h3 class="text-xl font-semibold mb-3 text">${area.title}</h3>
                    <p class="text-gray-400">${area.description}</p>
                `;
                grid.appendChild(card);
            });
        }
    </script>
</body>

</html>